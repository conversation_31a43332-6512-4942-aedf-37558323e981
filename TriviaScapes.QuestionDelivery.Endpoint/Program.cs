using System.Threading.Tasks;
using Autofac;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Xm.ApplicationEngine.NetCore;
using Xm.ApplicationEngine.NetCore.Config;
using Xm.AspNetCore;

namespace TriviaScapes.QuestionDelivery.Endpoint
{
    public class Program : AspNetProgram
    {
        public static Task Main(string[] args)
        {
            return new Program().RunAsync(args);
        }

        protected override Task WarmUpAsync(ILifetimeScope scope)
        {
#if XM_EXPERIMENTAL
            Lr.Basic.MessageHandling.MessageHandler.Info("Program", "Running experimental build");
#endif

            return base.WarmUpAsync(scope);
        }

        protected override IHostBuilder CreateHostBuilder(string[] args)
        {
            return Host.CreateDefaultBuilder(args)
                       .UseApplicationEngine<QuestionDeliveryApplicationEngine>(b => b.UseLogging().UseConsole().UseSerilog().AutoCreateServiceConfigByProjectGroup("QuestionDelivery", ConfigProjectUnit.Endpoint))
                       .ConfigureWebHostDefaults(webBuilder =>
                       {
                           webBuilder.UseIISIntegration().UseStartup<Startup>().ConfigureKestrelXmDefaults();
                       });
        }
    }
}