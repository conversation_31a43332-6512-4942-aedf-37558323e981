using System;
using System.Collections.Generic;
using System.Linq;
using TriviaScapes.ClientIntegration.Contracts.Gameplays;
using TriviaScapes.QuestionDelivery.ClientIntegration.Contracts;
using TriviaScapes.QuestionDelivery.Endpoint.Storages.Interactions;
using TriviaScapes.QuestionDelivery.Endpoint.Storages.Issue;
using Xm.Aerospike.Extensions.LargeObjects.Lists;
using Xm.QuestionDelivery;
using Xm.QuestionDelivery.Interactions;
using Xm.QuestionDelivery.Interactions.Storages;
using Xm.QuestionDelivery.Issue;
using Xm.QuestionDelivery.Issue.Storages;

namespace TriviaScapes.QuestionDelivery.Endpoint
{
    internal class QuestionDeliveryContextAdapter : IXmQuestionDeliveryContextAdapter<IQuestionDeliveryContextClientIntegration>
    {
        private readonly IAerospikeLargeListStorageFactory<IssuedQuestionStorageModel> _issueStorageFactory;
        private readonly IAerospikeLargeListStorageFactory<QuestionInteractionResultStorageModel> _interactionResultStorageFactory;

        public QuestionDeliveryContextAdapter(
            IAerospikeLargeListStorageFactory<IssuedQuestionStorageModel> issueStorageFactory,
            IAerospikeLargeListStorageFactory<QuestionInteractionResultStorageModel> interactionResultStorageFactory
        )
        {
            _issueStorageFactory = issueStorageFactory;
            _interactionResultStorageFactory = interactionResultStorageFactory;
        }

        public IEqualityComparer<IQuestionDeliveryContextClientIntegration> EqualityComparer => QuestionDeliveryContextEqualityComparer.Instance;

        public bool CanReIssueNotInteracted(IQuestionDeliveryContextClientIntegration context, IXmIssuedQuestion<IQuestionDeliveryContextClientIntegration> question)
        {
            return EqualityComparer.Equals(context, question.Context);
        }

        public IXmUserQuestionInteractionResultStorage<IQuestionDeliveryContextClientIntegration> CreateUserInteractionResultStorage(Guid userTrackingId)
        {
            return new UserQuestionInteractionResultStorage(_interactionResultStorageFactory, userTrackingId);
        }

        public IXmUserIssuedQuestionStorage<IQuestionDeliveryContextClientIntegration> CreateUserIssuedStorage(Guid userTrackingId)
        {
            return new UserIssuedQuestionStorage(_issueStorageFactory, userTrackingId);
        }

        public IEnumerable<IXmQuestionInteractionResult<IQuestionDeliveryContextClientIntegration>> GetAllowedToIssueInteractionResults(IQuestionDeliveryContextClientIntegration context, IEnumerable<IXmQuestionInteractionResult<IQuestionDeliveryContextClientIntegration>> results)
        {
            if (context.GameplayType == GameplayType.TriviaMatrix)
            {
                // always reissue same questions for trivia matrix gameplay
                return results.Where(r => EqualityComparer.Equals(r.Context, context));
            }

            if (context.GameplayType == GameplayType.QuizBlitz)
            {
                // temporary replay of the same questions for Quiz Blitz with no extra conditions
                return results.Where(r => EqualityComparer.Equals(r.Context, context));
            }

            return Enumerable.Empty<IXmQuestionInteractionResult<IQuestionDeliveryContextClientIntegration>>();
        }

        public string GetEasinessFactorStorageKey(IQuestionDeliveryContextClientIntegration context)
        {
            return "default";
        }

        public Dictionary<string, string> GetRotationTrackingInfo(IXmQuestionIssueQuery<IQuestionDeliveryContextClientIntegration> query)
        {
            return new Dictionary<string, string>
            {
                ["tileid"] = query?.Context?.TileId?.ToString(),
                ["gameid"] = query?.Context?.GameId
            };
        }

        public bool IsNewInteractionResult(IXmQuestionInteractionResult<IQuestionDeliveryContextClientIntegration> newInteractionResult, IReadOnlyList<IXmQuestionInteractionResult<IQuestionDeliveryContextClientIntegration>> existingInteractionResults)
        {
            var latestResult = existingInteractionResults.Where(r => EqualityComparer.Equals(r.Context, newInteractionResult.Context)).OrderByDescending(r => r.InteractedOn).FirstOrDefault();

            return latestResult == null || latestResult.InteractedOn.AddHours(24) < newInteractionResult.InteractedOn;
        }

        public bool IsNewIssued(IXmIssuedQuestion<IQuestionDeliveryContextClientIntegration> newQuestion, IReadOnlyList<IXmIssuedQuestion<IQuestionDeliveryContextClientIntegration>> existingQuestions)
        {
            var latestIssued = existingQuestions.Where(q => EqualityComparer.Equals(q.Context, newQuestion.Context)).OrderByDescending(q => q.IssuedOn).FirstOrDefault();

            return latestIssued == null || latestIssued.IssuedOn.AddHours(24) < newQuestion.IssuedOn;
        }

        public XmQuestionIssueGameContext GetIssueGameContext(IQuestionDeliveryContextClientIntegration context)
        {
            return new XmQuestionIssueGameContext()
            {
                LevelId = context.LevelId,
                Section = GetSection(context),
                GameInstanceId = context.GameId,
                GameType = context.GameplayType.ToString()
            };
        }

        bool IXmQuestionDeliveryContextAdapter<IQuestionDeliveryContextClientIntegration>.IsSuitableForEasinessFactorCalculation(IQuestionDeliveryContextClientIntegration context)
        {
            return context.GameplayType == GameplayType.Core;
        }

        private static string GetSection(IQuestionDeliveryContextClientIntegration context)
        {
            if (context.GameId?.StartsWith("challenge", StringComparison.OrdinalIgnoreCase) == true)
                return "gamechallenge";
            return "core";
        }
    }
}