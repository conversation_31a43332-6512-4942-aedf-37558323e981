using Autofac;
using TriviaScapes.QuestionDelivery.Endpoint.LevelQuestions.Config;
using TriviaScapes.QuestionDelivery.Presets;
using Xm.ApplicationEngine;
using Xm.ApplicationEngine.Config;

namespace TriviaScapes.QuestionDelivery.Endpoint.LevelQuestions;

internal class LevelQuestionEngineModule : EngineModule
{
    public override void RegisterConfigTypes(IConfigStorageBuilder configStorageBuilder)
    {
        base.RegisterConfigTypes(configStorageBuilder);

        var localLayerConfigAutoCreationInfo = new ConfigAutoCreationInfo(LevelQuestionAnnotationConstants.GetConfigGroup(), LevelQuestionAnnotationConstants.ConfigUnit);
        var localLayer = configStorageBuilder.BuildLocalSharedLayer(LevelQuestionConfigConstants.ConfigLayer, localLayerConfigAutoCreationInfo);

        localLayer.RegisterConfigType<LevelQuestionConfiguration>();
        localLayer.RegisterVariantSource(new LevelQuestionProfileVariantSource());

        localLayer.RegisterConfigType<LevelQuestionTypeConfiguration>();
    }

    protected override void RegisterComponents(ContainerBuilder builder)
    {
        base.RegisterComponents(builder);

        builder.RegisterType<LevelQuestionProfileConfigRepository>()
               .As<ILevelQuestionProfileRepository>()
               .SingleInstance();
    }
}