using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;

namespace TriviaScapes.QuestionDelivery.Endpoint.LevelQuestions.Config;

[DisplayName("Level Question Types")]
internal class LevelQuestionTypeConfiguration
{
    [ConfigUnique]
    [ConfigDisplayStyle(IsHeader = true,DisplayStyle = DisplayStyle.Inline)]
    [ConfigCollectionDisplayKey]
    [ConfigCollectionKey]
    public string[] List { get; private set; }
}