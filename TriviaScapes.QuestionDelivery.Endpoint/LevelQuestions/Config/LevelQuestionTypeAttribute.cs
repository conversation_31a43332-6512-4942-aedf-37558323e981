using System;
using System.Collections.Generic;
using System.Linq;
using Lr.Basic.Config;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.TreeObjects;

namespace TriviaScapes.QuestionDelivery.Endpoint.LevelQuestions.Config;

[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
internal sealed class LevelQuestionTypeAttribute : Attribute, IConfigContextVariants
{
    public string GetName(IConfigSerializerContext context)
    {
        return GetType().Name;
    }

    public IEnumerable<VariantDescription> GetVariants(IConfigSerializerContext context)
    {
        var types = context.Container.Get<LevelQuestionTypeConfiguration>()?.List;

        return types?.Select(c => new VariantDescription()
        {
            DisplayName = c,
            Value = c,
            Name = c
        }) ?? Enumerable.Empty<VariantDescription>();
    }
}