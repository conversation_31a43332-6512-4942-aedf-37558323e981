using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;

namespace TriviaScapes.QuestionDelivery.Endpoint.LevelQuestions.Config;

internal class LevelQuestionLanguageLevelSettings
{
    [ConfigDisplayStyle(IsHeader = true)]
    [LevelQuestionType]
    public string Type { get; private set; }

    [ConfigDisplayStyle(IsHeader = true)]
    public string Reference { get; private set; }

    [ConfigDisplayStyle(IsHeader = true, DisplayStyle = DisplayStyle.MultiLine, Height = 300)]
    public string QuestionIds { get; private set; }
}