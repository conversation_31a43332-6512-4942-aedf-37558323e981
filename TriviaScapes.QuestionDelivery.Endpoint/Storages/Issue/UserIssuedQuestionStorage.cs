using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Xm.Aerospike.Extensions.LargeObjects.Lists;
using Xm.QuestionDelivery.Issue;
using Xm.QuestionDelivery.Issue.Storages;
using TriviaScapes.QuestionDelivery.ClientIntegration.Contracts;

namespace TriviaScapes.QuestionDelivery.Endpoint.Storages.Issue
{
    internal class UserIssuedQuestionStorage : IXmUserIssuedQuestionStorage<IQuestionDeliveryContextClientIntegration>
    {
        // 58 bytes for record. 1048576 / 58 = 18079
        private static readonly AerospikeLargeListStorageSettings _settings = new()
        {
            ChunkSize = 100,
            ModificationRetries = 25,
            IndexMaxSize = 4000,
            DocumentMaxSize = 5000
        };

        private readonly IAerospikeLargeListStorage<IssuedQuestionStorageModel> _storage;

        public UserIssuedQuestionStorage(IAerospikeLargeListStorageFactory<IssuedQuestionStorageModel> storageFactory, Guid userTrackingId)
        {
            _storage = storageFactory.Create(userTrackingId, _settings);
        }

        public Task AddAsync(IReadOnlyCollection<IXmIssuedQuestion<IQuestionDeliveryContextClientIntegration>> questions, CancellationToken cancellationToken)
        {
            return _storage.AddRangeAsync(questions.Select(i => new IssuedQuestionStorageModel(i)));
        }

        public async Task<IReadOnlyCollection<IXmIssuedQuestion<IQuestionDeliveryContextClientIntegration>>> GetAllAsync(CancellationToken cancellationToken)
        {
            var result = await _storage.GetAllAsync().ConfigureAwait(false);

            return result.Items;
        }
    }
}