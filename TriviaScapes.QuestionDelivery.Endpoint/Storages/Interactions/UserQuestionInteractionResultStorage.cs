using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Xm.Aerospike.Extensions.LargeObjects.Lists;
using Xm.QuestionDelivery.Interactions;
using Xm.QuestionDelivery.Interactions.Storages;
using TriviaScapes.QuestionDelivery.ClientIntegration.Contracts;

namespace TriviaScapes.QuestionDelivery.Endpoint.Storages.Interactions
{
    internal class UserQuestionInteractionResultStorage : IXmUserQuestionInteractionResultStorage<IQuestionDeliveryContextClientIntegration>
    {
        // 23 bytes for record. 1048576 / 23 = 45590
        private static readonly AerospikeLargeListStorageSettings _settings = new()
        {
            ChunkSize = 100,
            ModificationRetries = 25,
            IndexMaxSize = 15000,
            DocumentMaxSize = 15000
        };

        private readonly IAerospikeLargeListStorage<QuestionInteractionResultStorageModel> _storage;

        public UserQuestionInteractionResultStorage(IAerospikeLargeListStorageFactory<QuestionInteractionResultStorageModel> storageFactory, Guid userTrackingId)
        {
            _storage = storageFactory.Create(userTrackingId, _settings);
        }

        public Task AddAsync(IReadOnlyCollection<IXmQuestionInteractionResult<IQuestionDeliveryContextClientIntegration>> interactions, CancellationToken cancellationToken)
        {
            return _storage.AddRangeAsync(interactions.Select(i => new QuestionInteractionResultStorageModel(i)));
        }

        public async Task<IReadOnlyCollection<IXmQuestionInteractionResult<IQuestionDeliveryContextClientIntegration>>> GetAllAsync(CancellationToken cancellationToken)
        {
            var result = await _storage.GetAllAsync().ConfigureAwait(false);

            return result.Items;
        }
    }
}