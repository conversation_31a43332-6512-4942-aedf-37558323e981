<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Configurations>Debug;Release;Experimental</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Xm.Presets.Aerospike" Version="6.4.273.367" />
    <PackageReference Include="Xm.Aerospike.Extensions.LargeObjects" Version="9.4.267.140" />
    <PackageReference Include="Xm.Commands.ApiGateway" Version="6.0.231.917" />
    <PackageReference Include="Xm.Contracts.Schemas.Integration" Version="6.1.241.291" />
    <PackageReference Include="Xm.Contracts.SourceGeneration.Net" Version="6.0.204.259">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Vx.ApiGateway.Integration" Version="6.0.265.479" />
    <PackageReference Include="Xm.AspNetCore" Version="6.3.274.8" />
    <PackageReference Include="Xm.Dev" Version="1.0.185.564">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Xm.Posts.Rotation.Logging.Integration" Version="6.1.266.635" />
    <PackageReference Include="Xm.QuestionDelivery.Commands" Version="6.4.282.239" />
  </ItemGroup>

  <Choose>
    <When Condition="'$(Configuration)'=='Experimental'">
      <PropertyGroup>
        <DefineConstants>XM_EXPERIMENTAL</DefineConstants>
      </PropertyGroup>
      <!-- Experimental configuration -->
      <ItemGroup>
      </ItemGroup>
    </When>
    <Otherwise>
      <!-- Production configuration -->
      <ItemGroup>
      </ItemGroup>
    </Otherwise>
  </Choose>

  <ItemGroup>
    <ProjectReference Include="..\TriviaScapes.QuestionDelivery.ClientIntegration.Contracts\TriviaScapes.QuestionDelivery.ClientIntegration.Contracts.csproj" />
    <ProjectReference Include="..\TriviaScapes.QuestionDelivery.Presets\TriviaScapes.QuestionDelivery.Presets.csproj" />
  </ItemGroup>

</Project>
