using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using TriviaScapes.QuestionDelivery.ClientIntegration.Contracts;
using Xm.AspNetCore.Security;
using Xm.QuestionDelivery;

namespace TriviaScapes.QuestionDelivery.Endpoint.Controllers;

[PrivateApi]
public class DebugController : ControllerBase
{
    private readonly IXmQuestionDeliveryContextAdapter<IQuestionDeliveryContextClientIntegration> _adapter;

    public DebugController(IXmQuestionDeliveryContextAdapter<IQuestionDeliveryContextClientIntegration> adapter)
    {
        _adapter = adapter;
    }

    [HttpGet]
    public async Task<IActionResult> GetAllInteractedQuestions(Guid id)
    {
        var storage = _adapter.CreateUserInteractionResultStorage(id);
        var interactions = await storage.GetAllAsync(CancellationToken.None).ConfigureAwait(false);
        return Ok(interactions);
    }

    [HttpGet]
    public async Task<IActionResult> GetAllIssuedQuestions(Guid id)
    {
        var storage = _adapter.CreateUserIssuedStorage(id);
        var issued = await storage.GetAllAsync(CancellationToken.None).ConfigureAwait(false);
        return Ok(issued);
    }
}
