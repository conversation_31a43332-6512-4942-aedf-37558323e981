using TriviaScapes.ClientIntegration.Contracts.Gameplays;
using Xm.Contracts.Abstractions;
using Xm.Telemetry.ClientIntegration.Contracts.Actions;

namespace TriviaScapes.Telemetry.ClientIntegration.Contracts
{
    [SerializableContract("tcilp")]
    public interface ITelemetryLevelProgressClientIntegration : IXmTelemetryActionContextSectionClientIntegration
    {
        [SerializableContractProperty("lid", 0)]
        int LevelId { get; }

        [SerializableContractProperty("lt", 1)]
        GameplayLevelType LevelType { get; }

        [SerializableContractProperty("gid", 2)]
        string GameId { get; }

        [SerializableContractProperty("a", 3)]
        int Attempt { get; }
    }
}