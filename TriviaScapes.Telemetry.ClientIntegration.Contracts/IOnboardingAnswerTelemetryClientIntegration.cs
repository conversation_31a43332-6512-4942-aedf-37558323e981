using System.Collections.Generic;
using Xm.Contracts.Abstractions;
using Xm.Telemetry.ClientIntegration.Contracts.Actions;

namespace TriviaScapes.Telemetry.ClientIntegration.Contracts
{
    [SerializableContract("tcioa")]
    public interface IOnboardingAnswerTelemetryClientIntegration : IXmTelemetryActionContextSectionClientIntegration
    {
        [SerializableContractProperty("q", 0)]
        string Question { get; }

        [SerializableContractProperty("a", 1)]
        IReadOnlyList<string> Answers { get; }
    }
}