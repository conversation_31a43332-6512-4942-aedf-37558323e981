<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>netstandard2.0</TargetFramework>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Xm.Contracts.Abstractions" Version="6.0.204.259" />
        <PackageReference Include="Xm.Dev" Version="1.0.185.564">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Xm.QuestionDelivery.Commands.Abstractions" Version="6.4.282.239" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\TriviaScapes.ClientIntegration.Contracts\TriviaScapes.ClientIntegration.Contracts.csproj" />
    </ItemGroup>

</Project>
