using System;
using System.Collections.Generic;
using System.Linq;
using Lr.Basic.MessageHandling;
using TriviaScapes.ClientIntegration.Contracts;
using TriviaScapes.ClientIntegration.Contracts.Gameplays;
using TriviaScapes.Telemetry.Actions.Endpoint.Extensions;
using TriviaScapes.Telemetry.ClientIntegration.Contracts;
using Xm.Advertisement.Telemetry.ClientIntegration.Contracts.Rewarded;
using Xm.Billing.Telemetry.ClientIntegration.Contracts;
using Xm.Cmp.Telemetry.ClientIntegration.Contracts;
using Xm.LevelMaps.Telemetry.ClientIntegration.Contracts;
using Xm.Telemetry.ClientIntegration.Contracts.Actions;
using Xm.Telemetry.ClientIntegration.Contracts.Data;
using Xm.Telemetry.ClientIntegration.Contracts.Inventory;
using Xm.Telemetry.Integration.Contracts;
using Xm.Telemetry.Integration.Contracts.AppState;
using Xm.Telemetry.Integration.Contracts.Cmp;
using Xm.Telemetry.Integration.Contracts.Navigation;
using Xm.Telemetry.Integration.Contracts.Onboarding;
using Xm.Telemetry.Integration.Contracts.Product;
using Xm.Telemetry.Integration.Contracts.Users;

namespace TriviaScapes.Telemetry.Actions.Endpoint.Populating
{
    internal static class XmTelemetryActionContainerClientIntegrationProxyExtensions
    {
        public static void PopulateAction(this XmTelemetryActionContainerClientIntegrationProxy<ITelemetryActionContextClientIntegration> proxy, XmTelemetryActionIntegrationDto action)
        {
            PopulateLevel(proxy, action);
            PopulateLevelDesign(proxy, action);
            PopulateProgress(proxy, action);
            PopulateRewardMultiplier(proxy, action);
            PopulateCmpConsentFromExtras(proxy, action);
            PopulateImageCollectionPicture(proxy, action);
            PopulateQuestionRoundsGameplayRound(proxy, action);
            PopulateProductFromInventoryItem(proxy, action);

            PopulateAppStateKeys(proxy, action);
            PopulateMapTileToExtras(proxy, action);
            PopulateGame(proxy, action);

            PopulateOnboardingAnswers(proxy, action);
        }

        private static void PopulateGame(IXmTelemetryActionContainerClientIntegrationProxy proxy, XmTelemetryActionIntegrationDto action)
        {
            if (action.AppVersion.ValueInt < AppReleaseVersions.NewGameplayStructure)
            {
                PopulateLegacyGame(proxy, action);

                return;
            }

            var levelProgress = proxy.GetContextSection<ITelemetryLevelProgressClientIntegration>();
            if (levelProgress != null)
            {
                if (action.Game == null)
                {
                    action.Game = new XmTelemetryGameIntegrationDto();
                }

                action.Game.InstanceId = levelProgress.GameId;
            }
        }

        [Obsolete("Since " + nameof(AppReleaseVersions.NewGameplayStructure))]
        private static void PopulateLegacyGame(IXmTelemetryActionContainerClientIntegrationProxy proxy, XmTelemetryActionIntegrationDto action)
        {
            var game = proxy.GetContextSection<IGameTelemetryClientIntegration>();
            if (game == null)
            {
                return;
            }

            if (action.Game == null)
            {
                action.Game = new XmTelemetryGameIntegrationDto();
            }

            action.Game.Type = game.GameplayType.ToString().ToLowerInvariant();
            action.Game.InstanceId = game.GameId;

            switch (game.GameplayType)
            {
                case GameplayType.QuestionRounds:
                    var round = proxy.GetContextSection<IQuestionRoundsGameplayRoundTelemetryClientIntegration>();

                    action.Game.Name = $"QuestionRounds_{round?.QuestionCategory}".ToLowerInvariant();

                    break;

                case GameplayType.TriviaMatrix:
                    action.Game.Name = "TriviaMatrix8".ToLowerInvariant();

                    break;

                case GameplayType.QuestionRush:
                    action.Game.Type = action.Game.Name = "CoinRush".ToLowerInvariant();

                    break;

                default:
                    action.Game.Name = game.GameplayType.ToString().ToLowerInvariant();
                    break;
            }
        }

        private static void PopulateLevel(IXmTelemetryActionContainerClientIntegrationProxy proxy, XmTelemetryActionIntegrationDto action)
        {
            var levelProgress = proxy.GetContextSection<ITelemetryLevelProgressClientIntegration>();
            if (levelProgress != null)
            {
                action.Level = new XmTelemetryLevelIntegrationDto
                {
                    Id = levelProgress.LevelId,
                    Type = levelProgress.LevelType.ToString().ToLowerInvariant(),
                    Attempt = levelProgress.Attempt
                };
            }
        }

        private static void PopulateLevelDesign(IXmTelemetryActionContainerClientIntegrationProxy proxy, XmTelemetryActionIntegrationDto action)
        {
            var levelDesign = proxy.GetContextSection<ITelemetryLevelDesignClientIntegration>();
            if (levelDesign != null)
            {
                action.Extras ??= new Dictionary<string, string>();

                action.Extras.Add("leveldesignname", levelDesign.Name);
                action.Extras.Add("leveldesignusagenumber", levelDesign.UsageNumber.ToString());
            }
        }

        private static void PopulateProgress(XmTelemetryActionContainerClientIntegrationProxy<ITelemetryActionContextClientIntegration> proxy, XmTelemetryActionIntegrationDto action)
        {
            action.Progress = GetInternalProgress(proxy.Context.UserProgress);
            action.PrevProgress = GetInternalProgress(proxy.Context.PrevUserProgress);
        }

        private static void PopulateRewardMultiplier(IXmTelemetryActionContainerClientIntegrationProxy proxy, XmTelemetryActionIntegrationDto action)
        {
            var rewardMultiplier = proxy.GetContextSection<ITelemetryRewardMultiplierClientIntegration>();
            if (rewardMultiplier != null)
            {
                action.Extras ??= new Dictionary<string, string>();

                action.Extras.Add("rewardmultiplier", rewardMultiplier.Name);
            }
        }

        private static XmTelemetryProgressIntegrationDto GetInternalProgress(ITelemetryUserProgressClientIntegration progress)
        {
            if (progress == null)
            {
                return null;
            }

            var result = new XmTelemetryProgressIntegrationDto
            {
                Items = new Dictionary<string, long>
                {
                    [XmTelemetryProgressNames.LevelsTotal] = progress.LevelsPassed ?? 0,
                    ["coins"] = progress.Coins ?? 0,
                    ["stars"] = progress.Stars ?? 0,
                    ["lives"] = progress.Lives ?? 0,
                    ["questionrightanswers"] = progress.TotalRightQuestionAnswers ?? 0,
                    ["questionwronganswers"] = progress.TotalWrongQuestionAnswers ?? 0,
                }
            };

            return result;
        }

        private static void PopulateCmpConsentFromExtras(IXmTelemetryActionContainerClientIntegrationProxy proxy, XmTelemetryActionIntegrationDto action)
        {
            var cmpConsent = proxy.GetContextSection<IXmCmpConsentTelemetryClientIntegration>();

            if (cmpConsent != null)
            {
                return;
            }

            if (proxy.Action?.Extras == null)
            {
                return;
            }

            if (proxy.Action.Extras.TryGetValue("cmp_ad", out var cmpAd))
            {
                action.Cmp ??= new XmTelemetryCmpIntegrationDto();
                action.Cmp.AdMode = cmpAd;
            }

            if (proxy.Action.Extras.TryGetValue("cmp_tc", out var tcString))
            {
                if (!string.IsNullOrWhiteSpace(tcString))
                {
                    action.Extras ??= new Dictionary<string, string>();
                    action.Extras.Add("cmp_tcstring_len", tcString.Length.ToString());
                }

                MessageHandler.Debug(nameof(PopulateCmpConsentFromExtras), "Cmp tc string", new { Project = action.Project, _userId = action.UserTrackingId, TCString = tcString });
            }
        }

        private static void PopulateImageCollectionPicture(XmTelemetryActionContainerClientIntegrationProxy<ITelemetryActionContextClientIntegration> proxy, XmTelemetryActionIntegrationDto action)
        {
            var imageCollectionPicture = proxy.GetContextSection<IImageCollectionPictureTelemetryClientIntegration>();

            if (imageCollectionPicture == null)
            {
                return;
            }

            action.Extras ??= new Dictionary<string, string>();

            action.Extras.Add("icp_name", imageCollectionPicture.Name);
            action.Extras.Add("icp_group", imageCollectionPicture.Group);
            action.Extras.Add("icp_rfc", imageCollectionPicture.RevealedFragmentCount.ToString());
            action.Extras.Add("icp_tfc", imageCollectionPicture.TotalFragmentCount.ToString());

            if (string.IsNullOrWhiteSpace(action.Addition) &&
                action.Section.Equals("gallery", StringComparison.OrdinalIgnoreCase) &&
                action.Category.Equals("images", StringComparison.OrdinalIgnoreCase) &&
                action.Object.Equals("picture_fragment", StringComparison.OrdinalIgnoreCase) &&
                action.Name.Equals("get", StringComparison.OrdinalIgnoreCase))
            {
                action.Addition = $"{imageCollectionPicture.RevealedFragmentCount}/{imageCollectionPicture.TotalFragmentCount}";
            }
        }

        private static void PopulateQuestionRoundsGameplayRound(XmTelemetryActionContainerClientIntegrationProxy<ITelemetryActionContextClientIntegration> proxy, XmTelemetryActionIntegrationDto action)
        {
            var questionRoundsGameplayRound = proxy.GetContextSection<IQuestionRoundsGameplayRoundTelemetryClientIntegration>();

            if (questionRoundsGameplayRound == null)
            {
                return;
            }

            action.Extras ??= new Dictionary<string, string>();

            action.Extras.Add("round_rn", questionRoundsGameplayRound.RoundNumber.ToString());
            action.Extras.Add("round_qc", questionRoundsGameplayRound.QuestionCategory);
        }

        private static void PopulateProductFromInventoryItem(XmTelemetryActionContainerClientIntegrationProxy<ITelemetryActionContextClientIntegration> proxy, XmTelemetryActionIntegrationDto action)
        {
            var inventoryItem = proxy.GetContextSection<IXmTelemetryInventoryItemClientIntegration>();
            var reward = proxy.GetContextSection<IXmRewardedAdvertisementRewardTelemetryClientIntegration>();
            var inapp = proxy.GetContextSection<IXmInAppProductTelemetryClientIntegration>();

            if (inventoryItem == null || reward != null || inapp != null)
            {
                return;
            }

            action.Product = new XmTelemetryProductIntegrationDto()
            {
                Name = inventoryItem.Name,
                Type = inventoryItem.Type
            };
        }

        private static void PopulateAppStateKeys(IXmTelemetryActionContainerClientIntegrationProxy proxy, XmTelemetryActionIntegrationDto action)
        {
            if (action.AppStateKeys == null && proxy.AppStateId > 0)
            {
                action.AppStateKeys = new List<XmTelemetryAppStateKeyIntegrationDto>()
                {
                    new XmTelemetryAppStateKeyIntegrationDto
                    {
                        Name = XmTelemetryDataUtils.GetName<ITelemetryActionAppStateClientIntegration>(),
                        Id = proxy.AppStateId
                    }
                };
            }
        }

        private static void PopulateMapTileToExtras(IXmTelemetryActionContainerClientIntegrationProxy proxy, XmTelemetryActionIntegrationDto action)
        {
            var mapTile = proxy.GetContextSection<IXmLevelMapTileTelemetryClientIntegration>();

            if (mapTile == null)
            {
                return;
            }

            action.Extras ??= new Dictionary<string, string>();

            action.Extras.Add("tile_id", mapTile.Id);
            action.Extras.Add("tile_logictype", mapTile.LogicType);
            action.Extras.Add("tile_attempt", mapTile.Attempt.ToString());
        }

        private static void PopulateOnboardingAnswers(IXmTelemetryActionContainerClientIntegrationProxy proxy, XmTelemetryActionIntegrationDto action)
        {
            if (action.AppVersion.ValueInt < AppReleaseVersions.OnboardingAnswerActionContextSection)
            {
                if (action.TryExtractLegacyOnboardingAnswer(out var legacyOnboardingAnswer))
                {
                    action.OnboardingAnswer = legacyOnboardingAnswer;
                }

                return;
            }

            var onboardingAnswer = proxy.GetContextSection<IOnboardingAnswerTelemetryClientIntegration>();
            if (onboardingAnswer != null)
            {
                action.OnboardingAnswer = new XmTelemetryOnboardingAnswerIntegrationDto()
                {
                    Question = onboardingAnswer.Question,
                    Answers = onboardingAnswer.Answers.ToList()
                };
            }
        }
    }
}