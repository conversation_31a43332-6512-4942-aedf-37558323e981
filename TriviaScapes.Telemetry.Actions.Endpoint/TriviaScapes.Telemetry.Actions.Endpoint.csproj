<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>


  <ItemGroup>

    <PackageReference Include="Xm.Contracts.Schemas.Integration" Version="6.1.241.291" />
    <PackageReference Include="Xm.Contracts.SourceGeneration.Net" Version="6.0.204.259">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Xm.Dev" Version="1.0.185.564">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>

    <PackageReference Include="Xm.Telemetry.Actions.Endpoint.Core" Version="6.4.284.508" />
    <PackageReference Include="Xm.UserInventory.Telemetry.ClientIntegration.Dto" Version="6.0.211.193" />
    <PackageReference Include="Xm.QuestionDelivery.Telemetry.ClientIntegration.Dto" Version="6.4.282.239" />
    <PackageReference Include="Xm.GameActivities.Telemetry.ClientIntegration.Dto" Version="6.1.284.834" />
    <PackageReference Include="Xm.Gameplays.Telemetry.ClientIntegration.Dto" Version="6.0.254.899" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TriviaScapes.ClientIntegration.Contracts\TriviaScapes.ClientIntegration.Contracts.csproj" />
    <ProjectReference Include="..\TriviaScapes.Telemetry.ClientIntegration.Contracts\TriviaScapes.Telemetry.ClientIntegration.Contracts.csproj" />
  </ItemGroup>

</Project>
