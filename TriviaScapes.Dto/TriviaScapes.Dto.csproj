<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <RunAnalyzers>false</RunAnalyzers>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MessagePack.Annotations" Version="2.5.140" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Xm.GameGoals.ClientIntegration.Dto" Version="6.0.239.270" />
    <PackageReference Include="Xm.Advertisement.Mediations.Amazon.ClientIntegration.Dto" Version="6.1.270.267" />
    <PackageReference Include="Xm.Billing.UserRewards.ClientIntegration.Dto" Version="6.1.286.840" />
    <PackageReference Include="Xm.Competitions.UserRecords.ClientIntegration.Dto" Version="6.0.203.59" />
    <PackageReference Include="Xm.Advertisement.Billing.ClientIntegration.Contracts" Version="6.1.270.267" />
    <PackageReference Include="Xm.Advertisement.Commands.Dto" Version="6.1.270.267" />
    <PackageReference Include="Xm.Billing.Commands.Dto" Version="6.1.286.840" />
    <PackageReference Include="Xm.ConfigExtensions.ClientIntegration.Dto" Version="6.0.202.933" />
    <PackageReference Include="Xm.ExperimentalFeatures.Commands.ClientIntegration.Dto" Version="6.1.256.347" />
    <PackageReference Include="Xm.GameActivities.Commands.ClientIntegration.Dto" Version="6.1.284.834" />
    <PackageReference Include="Xm.GameActivities.UserRecords.ClientIntegration.Dto" Version="6.1.284.834" />
    <PackageReference Include="Xm.GameActivities.Attempts.ClientIntegration.Dto" Version="6.1.284.834" />
    <PackageReference Include="Xm.GameLives.ClientIntegration.Dto" Version="6.0.208.899" />
    <PackageReference Include="Xm.LevelMaps.ClientIntegration.Dto" Version="6.1.249.541" />
    <PackageReference Include="Xm.Presets.Commands.Dto" Version="6.4.273.367" />
    <PackageReference Include="Xm.Gameplays.ClientIntegration.Dto" Version="6.0.254.899" />
    <PackageReference Include="Xm.Gameplays.Commands.ClientIntegration.Dto" Version="6.0.254.899" />
    <PackageReference Include="Xm.Dev" Version="1.0.185.564">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Xm.Contracts.SourceGeneration.Net" Version="6.0.204.259">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Xm.QuestionDelivery.LevelMaps.ClientIntegration.Dto" Version="6.4.282.239" />
    <PackageReference Include="Xm.SocialInteraction.ClientIntegration.Dto" Version="6.0.202.830" />
    <PackageReference Include="Xm.UserAchievements.ClientIntegration.Dto" Version="6.1.235.370" />
    <PackageReference Include="Xm.UserAchievements.Commands.ClientIntegration.Dto" Version="6.1.235.370" />
    <PackageReference Include="Xm.UserInventory.UserRecords.ClientIntegration.Dto" Version="6.0.211.193" />
    <PackageReference Include="Xm.UserRewards.Commands.ClientIntegration.Dto" Version="6.0.224.877" />
    <PackageReference Include="Xm.ReviewManager.UserRecords.Dto" Version="6.0.202.956" />
    <PackageReference Include="Xm.UserProfiles.UserRecords.Dto" Version="6.0.207.517" />
    <PackageReference Include="Xm.PromoOffers.Commands.ClientIntegration.Dto" Version="6.1.271.611" />
    <PackageReference Include="Xm.PromoOffers.GameActivities.ClientIntegration.Dto" Version="6.1.271.611" />
    <PackageReference Include="Xm.Advertisement.GameActivities.ClientIntegration.Dto" Version="6.1.270.267" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TriviaScapes.ClientIntegration.Contracts\TriviaScapes.ClientIntegration.Contracts.csproj" />
  </ItemGroup>

</Project>
