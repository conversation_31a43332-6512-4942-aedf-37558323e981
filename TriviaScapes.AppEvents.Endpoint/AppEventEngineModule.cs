using Xm.AppEvents.Registration;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.AppEvents.Endpoint
{
    public class AppEventEngineModule : XmAppEventEngineModule
    {
        private readonly ISerializableContractRepository _serializableContractRepository;

        public AppEventEngineModule(ISerializableContractRepository serializableContractRepository)
        {
            _serializableContractRepository = serializableContractRepository;
        }

        protected override void BuildComponents(IXmAppEventComponentBuilder builder)
        {
            builder.UseAerospikeMessagePackStorage(_serializableContractRepository)
                   .AddCompetitions()
                   .AddPresets()
                   .AddUserCompensations();
        }
    }
}