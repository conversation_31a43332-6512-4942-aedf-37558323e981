using Xm.Contracts.SourceGeneration;

[assembly: SerializableContractImplementationReference("Xm.AppEvents.Commands.ClientIntegration.Dto")]
[assembly: SerializableContractImplementationReference("Xm.Competitions.AppEvents.ClientIntegration.Dto")]
[assembly: SerializableContractImplementationReference("Xm.Presets.AppEvents.ClientIntegration.Dto")]
[assembly: SerializableContractImplementationReference("Xm.UserCompensations.AppEvents.ClientIntegration.Dto")]

[assembly: ImplementSerializableContracts("Xm.AppEvents.ClientIntegration.Contracts", null, "Dto", "AppEventSerializableContractRepository")]
[assembly: ImplementSerializableContracts("Xm.AppEvents.Commands.ClientIntegration.Contracts", null, "Dto", "AppEventSerializableContractRepository")]
[assembly: ImplementSerializableContracts("Xm.Competitions.AppEvents.ClientIntegration.Contracts", null, "Dto", "AppEventSerializableContractRepository")]
[assembly: ImplementSerializableContracts("Xm.Presets.AppEvents.ClientIntegration.Contracts", null, "Dto", "AppEventSerializableContractRepository")]
[assembly: ImplementSerializableContracts("TriviaScapes.AppEvents.ClientIntegration.Contracts", null, "Dto", "AppEventSerializableContractRepository")]
[assembly: ImplementSerializableContracts("Xm.UserCompensations.AppEvents.ClientIntegration.Contracts", null, "Dto", "AppEventSerializableContractRepository")]