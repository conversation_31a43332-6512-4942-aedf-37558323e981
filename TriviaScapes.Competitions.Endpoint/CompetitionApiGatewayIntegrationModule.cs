using Autofac;
using Vx.ApiGateway.Integration;
using Xm.ApiGateway.Integration;
using Xm.Commands;
using Xm.Commands.ApiGateway;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.Competitions.Endpoint
{
    internal class CompetitionApiGatewayIntegrationModule : VxApiGatewayIntegrationModule
    {
        public CompetitionApiGatewayIntegrationModule(ISerializableContractRepository serializableContractRepository)
            : base(serializableContractRepository)
        {
        }

        protected override void Configure(IVxApiGatewayIntegrationRegistrar configurator)
        {
            configurator.SetHttp(ctx =>
            {
                var commandHandlerFactory = ctx.Resolve<IXmCommandHandlerFactory>();

                return (apiGatewayContext, cancellationToken) => HandleAsync(commandHandlerFactory, apiGatewayContext, cancellationToken);
            });
        }

        private static Task HandleAsync(IXmCommandHandlerFactory commandHandlerFactory, IXmApiGatewayApiContext context, CancellationToken cancellationToken)
        {
            var commandContext = new CompetitionCommandContext()
            {
                UserIdentity = context.Session.UserIdentity,
                ClientAppStart = context.Session.ClientAppStart
            };

            var commandProcessor = new XmApiGatewayCommandProcessor(context, commandContext, commandHandlerFactory);

            return commandProcessor.ProcessAsync(cancellationToken);
        }
    }
}