using Xm.Contracts.SourceGeneration;

[assembly: SerializableContractImplementationReference("Xm.Competitions.Commands.ClientIntegration.Dto")]
[assembly: SerializableContractImplementationReference("Xm.Competitions.Seasons.ClientIntegration.Dto")]

[assembly: ImplementSerializableContracts("Xm.Competitions.ClientIntegration.Contracts", null, "Dto", "CompetitionSerializableContractRepository")]
[assembly: ImplementSerializableContracts("Xm.Competitions.Commands.ClientIntegration.Contracts", null, "Dto", "CompetitionSerializableContractRepository")]
[assembly: ImplementSerializableContracts("Xm.Competitions.Seasons.ClientIntegration.Contracts", null, "Dto", "CompetitionSerializableContractRepository")]

[assembly: ImplementSerializableContracts("TriviaScapes.Competitions.ClientIntegration.Contracts", null, "Dto", "CompetitionSerializableContractRepository")]
