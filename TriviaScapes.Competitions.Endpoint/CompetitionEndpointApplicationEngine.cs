using Autofac;
using Xm.Aerospike.Engine;
using Xm.ApplicationEngine.Engines;
using Xm.Commands;
using Xm.Competitions.Registration;
using Xm.Competitions.ScoreKeeper.Integration;
using Xm.Contracts.Schemas.Integration;
using Xm.EventBus.RoutingModule;
using Xm.Utils;
using Xm.Utils.Runtime;

namespace TriviaScapes.Competitions.Endpoint
{
    public class CompetitionEndpointApplicationEngine : DefaultApplicationEngine
    {
        protected override void RegisterModules(ContainerBuilder modulesBuilder)
        {
            base.RegisterModules(modulesBuilder);

            RegisterModule(modulesBuilder, _ => new XmCommandModule<CompetitionCommandContext>(CompetitionSerializableContractRepository.Instance));

            RegisterModule(modulesBuilder, _ => new CompetitionApiGatewayIntegrationModule(CompetitionSerializableContractRepository.Instance));

            RegisterModule<XmScoreKeeperIntegrationModule>(modulesBuilder);

            RegisterModule(modulesBuilder,
                _ => new XmCompetitionEngineModule(
                    CompetitionSerializableContractRepository.Instance,
                    b => b.UseAerospikeContestantPerformanceStateStorage().AddSeasons()
                )
            );

            RegisterModule(modulesBuilder, _ => new AerospikeConfigurationModule(ApplicationRuntimeInfo.Current.ProjectGroup.FirstUpper()));
            RegisterModule<AerospikeServiceModule>(modulesBuilder);

            RegisterModule(modulesBuilder, _ => new XmEventBusRoutingModule());
            RegisterModule<SerializableContractSchemaIntegrationEngineModule>(modulesBuilder);
        }
    }
}