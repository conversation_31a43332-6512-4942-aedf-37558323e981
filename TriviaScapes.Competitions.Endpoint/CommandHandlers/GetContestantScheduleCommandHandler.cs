using Xm.Commands;
using Xm.Competitions;
using Xm.Competitions.Commands.CommandHandlers;

namespace TriviaScapes.Competitions.Endpoint.CommandHandlers
{
    public class GetContestantScheduleCommandHandler : XmGetContestantScheduleCommandHandler<CompetitionCommandContext>
    {
        public GetContestantScheduleCommandHandler(IXmCommandContainer commandContainer, CompetitionCommandContext context, IXmCompetitionIntegration competitions)
            : base(commandContainer, context, competitions)
        {
        }
    }
}