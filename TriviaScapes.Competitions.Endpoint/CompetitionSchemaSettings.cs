using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.Competitions.ClientIntegration.Contracts;
using Xm.Competitions;
using Xm.Competitions.Events.Scheduling;
using Xm.Competitions.Schemas;
using Xm.Competitions.Schemas.Extensions;
using Xm.Competitions.Schemas.Total;
using Xm.Competitions.Seasons.Schemas;

namespace TriviaScapes.Competitions.Endpoint
{
    [ConfigInherit]
    [DisplayName("Default")]
    public class CompetitionSchemaSettings : IXmCompetitionSchema
    {
        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public XmCompetitionSeasonSchemaSetSettings Seasons { get; internal set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public XmTotalCompetitionSchemaSettings Total { get; internal set; }

        public XmCompetitionScheduleSegmentSet GetScheduleSegmentSet(IXmCompetitionSchemaExtensionContainer extensionContainer, XmCompetitionSelectionContext context, DateTime date)
        {
            var seasonScheduleSegmentSet = Seasons.GetScheduleSegmentSet(extensionContainer, context, date);

            return new XmCompetitionScheduleSegmentSet()
            {
                [CompetitionType.Total] = Total?.GetScheduleSegment(),
                [CompetitionType.Season] = seasonScheduleSegmentSet.Season,
                [CompetitionType.SeasonStage] = seasonScheduleSegmentSet.SeasonStage
            };
        }
    }
}