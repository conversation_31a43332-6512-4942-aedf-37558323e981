include:
  - project: 'infrastructure/ci/docker'
    file: 'ci-template.yaml'
    ref: 'version/0.1'
  - local: 'Legacy.v1.gitlab-ci.yml'
  - local: '.gitlab/build/prod.yml'


stages:
  - init
  - build
  - deploy
  - deploy-experimental
  - child-pipeline-generator
  - child-pipeline-trigger

build:
  extends: [.docker-job-template]
  stage: build
  variables:
    DOCKER_IMAGE_DOCKERFILE: Build.Dockerfile
    DOCKER_BUILD_ARGS: "build_id=$CI_JOB_ID,base_version=6.0"
  only:
  - production_alpha
  - production_beta
  - production
  - production_appevents
  - production_competitions
  - production_telemetry_alpha
  - production_telemetry_beta
  - production_telemetry
  - production_notifications
  - production_notifications_alpha
  - production_questiondelivery
  - production_application
  - production_questiondelivery_alpha

build-experimental:
  extends: [.docker-job-template]
  stage: build
  variables:
    DOCKER_IMAGE_DOCKERFILE: Build.Dockerfile
    DOCKER_BUILD_ARGS: "build_id=$CI_JOB_ID,base_version=6.0,configuration=Experimental"
  only:
  - production_alpha
  - production_beta
  - production
  - production_questiondelivery
  - production_questiondelivery_alpha

deploy-api-alpha:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: application_endpoint_alpha
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.Endpoint/Docker/Alpha/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "stage.testik.org"
    DOCKERCI_GROUP: "triviascapes-alpha"
    DOCKERCI_PRESET: "application-endpoint"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production_alpha

deploy-progresses-alpha:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: progresses_endpoint_alpha
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.Progresses.Endpoint/Docker/Alpha/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "stage.testik.org"
    DOCKERCI_GROUP: "triviascapes-alpha"
    DOCKERCI_PRESET: "progresses-endpoint"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production_alpha

deploy-questiondelivery-alpha:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: questiondelivery_endpoint_alpha
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.QuestionDelivery.Endpoint/Docker/Alpha/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "stage.testik.org"
    DOCKERCI_GROUP: "triviascapes-alpha"
    DOCKERCI_PRESET: "question-delivery-endpoint"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production_alpha
  - production_questiondelivery_alpha

deploy-questiondelivery-experimental-alpha:
  extends: [.docker-job-template]
  stage: deploy-experimental
  variables:
    DOCKER_IMAGE_NAME: questiondelivery_endpoint_experimental_alpha
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.QuestionDelivery.Endpoint/Docker/Alpha/Dockerfile
    DOCKER_BUILD_ARGS: "configuration=Experimental"
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "stage.testik.org"
    DOCKERCI_GROUP: "triviascapes-alpha"
    DOCKERCI_PRESET: "question-delivery-endpoint-exp"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production_alpha
  - production_questiondelivery_alpha

telemetry-actions-alpha:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: telemetry_actions_endpoint_alpha
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.Telemetry.Actions.Endpoint/Docker/Alpha/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "stage.testik.org"
    DOCKERCI_GROUP: "triviascapes-alpha"
    DOCKERCI_PRESET: "telemetry-actions-endpoint"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production_alpha
  - production_telemetry_alpha
  - production_telemetry_stage

deploy-notifications-scheduler-alpha:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: notification_scheduler_alpha
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.Notifications.Scheduler/Docker/Alpha/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "stage.testik.org"
    DOCKERCI_GROUP: "triviascapes-alpha"
    DOCKERCI_PRESET: "notifications-scheduler"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production_alpha
  - production_notifications_alpha

deploy-appevents-alpha:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: appevents_endpoint_alpha
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.AppEvents.Endpoint/Docker/Alpha/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "stage.testik.org"
    DOCKERCI_GROUP: "triviascapes-alpha"
    DOCKERCI_PRESET: "appevents-endpoint"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
    - production_alpha

#TODO: uncomments when fix
#deploy-competitions-alpha:
#  extends: [.docker-job-template]
#  stage: deploy
    #  variables:
    #DOCKER_IMAGE_NAME: competitions_endpoint_alpha
    #DOCKER_IMAGE_DOCKERFILE: TriviaScapes.Competitions.Endpoint/Docker/Alpha/Dockerfile
    #DOCKER_IMAGE_PUSH: "true"
    #DOCKERCI_DEPLOY: "true"
    #DOCKERCI_CLUSTER: "stage.testik.org"
    #DOCKERCI_GROUP: "triviascapes-alpha"
    #DOCKERCI_PRESET: "competitions-endpoint"
    #DOCKERCI_JOBID: $CI_JOB_ID
    #only:
  #- production_alpha

deploy-api-beta:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: application_endpoint_beta
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.Endpoint/Docker/Beta/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "stage.testik.org"
    DOCKERCI_GROUP: "triviascapes-beta"
    DOCKERCI_PRESET: "application-endpoint"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production_beta

deploy-progresses-beta:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: progresses_endpoint_beta
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.Progresses.Endpoint/Docker/Beta/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "stage.testik.org"
    DOCKERCI_GROUP: "triviascapes-beta"
    DOCKERCI_PRESET: "progresses-endpoint"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production_beta

deploy-questiondelivery-beta:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: questiondelivery_endpoint_beta
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.QuestionDelivery.Endpoint/Docker/Beta/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "stage.testik.org"
    DOCKERCI_GROUP: "triviascapes-beta"
    DOCKERCI_PRESET: "question-delivery-endpoint"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production_beta

deploy-questiondelivery-experimental-beta:
  extends: [.docker-job-template]
  stage: deploy-experimental
  variables:
    DOCKER_IMAGE_NAME: questiondelivery_endpoint_experimental_beta
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.QuestionDelivery.Endpoint/Docker/Beta/Dockerfile
    DOCKER_BUILD_ARGS: "configuration=Experimental"
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "stage.testik.org"
    DOCKERCI_GROUP: "triviascapes-beta"
    DOCKERCI_PRESET: "question-delivery-endpoint-exp"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production_beta

telemetry-actions-beta:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: telemetry_actions_endpoint_beta
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.Telemetry.Actions.Endpoint/Docker/Beta/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "stage.testik.org"
    DOCKERCI_GROUP: "triviascapes-beta"
    DOCKERCI_PRESET: "telemetry-actions-endpoint"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production_beta
  - production_telemetry_beta
  - production_telemetry_stage
  
deploy-api:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: application_endpoint
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.Endpoint/Docker/Production/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "de1.appik.org"
    DOCKERCI_GROUP: "triviascapes"
    DOCKERCI_PRESET: "application-endpoint"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production
  - production_application

deploy-questiondelivery:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: questiondelivery_endpoint
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.QuestionDelivery.Endpoint/Docker/Production/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "de1.appik.org"
    DOCKERCI_GROUP: "triviascapes"
    DOCKERCI_PRESET: "question-delivery-endpoint"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production
  - production_questiondelivery

deploy-questiondelivery-experimental:
  extends: [.docker-job-template]
  stage: deploy-experimental
  variables:
    DOCKER_IMAGE_NAME: questiondelivery_endpoint_experimental
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.QuestionDelivery.Endpoint/Docker/Production/Dockerfile
    DOCKER_BUILD_ARGS: "configuration=Experimental"
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "de1.appik.org"
    DOCKERCI_GROUP: "triviascapes"
    DOCKERCI_PRESET: "question-delivery-endpoint-exp"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production
  - production_questiondelivery

deploy-progresses:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: progresses_endpoint
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.Progresses.Endpoint/Docker/Production/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "de1.appik.org"
    DOCKERCI_GROUP: "triviascapes"
    DOCKERCI_PRESET: "progresses-endpoint"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production

actions-telemetry:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: telemetry_actions_endpoint
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.Telemetry.Actions.Endpoint/Docker/Production/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "de1.appik.org"
    DOCKERCI_GROUP: "triviascapes"
    DOCKERCI_PRESET: "telemetry-actions-endpoint"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production
  - production_telemetry

deploy-notifications-scheduler:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: notification_scheduler
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.Notifications.Scheduler/Docker/Production/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "de1.appik.org"
    DOCKERCI_GROUP: "triviascapes"
    DOCKERCI_PRESET: "notifications-scheduler"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production
  - production_notifications

deploy-appevents:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: appevents_endpoint
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.AppEvents.Endpoint/Docker/Production/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "de1.appik.org"
    DOCKERCI_GROUP: "triviascapes"
    DOCKERCI_PRESET: "appevents-endpoint"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production
  - production_appevents

deploy-competitions:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_IMAGE_NAME: competitions_endpoint
    DOCKER_IMAGE_DOCKERFILE: TriviaScapes.Competitions.Endpoint/Docker/Production/Dockerfile
    DOCKER_IMAGE_PUSH: "true"
    DOCKERCI_DEPLOY: "true"
    DOCKERCI_CLUSTER: "de1.appik.org"
    DOCKERCI_GROUP: "triviascapes"
    DOCKERCI_PRESET: "competitions-endpoint"
    DOCKERCI_JOBID: $CI_JOB_ID
  only:
  - production
  - production_competitions

deploy-unity-dto:
  extends: [.docker-job-template]
  stage: deploy
  variables:
    DOCKER_BUILDKIT: 0
    DOCKER_IMAGE_DOCKERFILE: UnityDto.Dockerfile
  only:
  - production_unity
