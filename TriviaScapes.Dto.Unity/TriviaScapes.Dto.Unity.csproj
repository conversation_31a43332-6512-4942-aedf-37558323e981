<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
    <RunAnalyzers>false</RunAnalyzers>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Xm.Adjust.Telemetry.Dto.Unity" Version="6.0.260.883" />
    <PackageReference Include="Xm.Advertisement.Mediations.Amazon.Dto.Unity" Version="6.1.270.267" />
    <PackageReference Include="Xm.AppEvents.Dto.Unity" Version="6.0.203.35" />
    <PackageReference Include="Xm.GameGoals.Dto.Unity" Version="6.0.239.270" />
    <PackageReference Include="Xm.Billing.UserRewards.Dto.Unity" Version="6.1.286.840" />
    <PackageReference Include="Xm.Cmp.Dto.Unity" Version="6.0.203.110" />
    <PackageReference Include="Xm.Competitions.AppEvents.Dto.Unity" Version="6.0.203.59" />
    <PackageReference Include="Xm.Competitions.Dto.Unity" Version="6.0.203.59" />
    <PackageReference Include="Xm.Competitions.Seasons.Dto.Unity" Version="6.0.203.59" />
    <PackageReference Include="Xm.Advertisement.Billing.ClientIntegration.Contracts" Version="6.1.270.267" />
    <PackageReference Include="Xm.Advertisement.Dto.Unity" Version="6.1.270.267" />
    <PackageReference Include="Xm.Billing.Dto.Unity" Version="6.1.286.840" />
    <PackageReference Include="Xm.ExperimentalFeatures.Dto.Unity" Version="6.1.256.347" />
    <PackageReference Include="Xm.GameLives.Dto.Unity" Version="6.0.208.899" />
    <PackageReference Include="Xm.Notifications.Firebase.Dto.Unity" Version="6.0.219.446" />
    <PackageReference Include="Xm.Presets.AppEvents.Dto.Unity" Version="6.4.273.367" />
    <PackageReference Include="Xm.Presets.Dto.Unity" Version="6.4.273.367" />
    <PackageReference Include="Xm.QuestionDelivery.Dto.Unity" Version="6.4.282.239" />
    <PackageReference Include="Xm.QuestionDelivery.LevelMaps.Dto.Unity" Version="6.4.282.239" />
    <PackageReference Include="Xm.ReviewManager.UserRecords.Dto.Unity" Version="6.0.202.956" />
    <PackageReference Include="Xm.Telemetry.Dto.Unity" Version="6.2.278.191" />
    <PackageReference Include="Xm.Attribution.Telemetry.Dto.Unity" Version="6.0.245.30" />
    <PackageReference Include="Xm.LevelMaps.Dto.Unity" Version="6.1.249.541" />
    <PackageReference Include="Xm.SocialInteraction.Dto.Unity" Version="6.0.202.830" />
    <PackageReference Include="Xm.UserAchievements.Dto.Unity" Version="6.1.235.370" />
    <PackageReference Include="Xm.UserCompensations.AppEvents.Dto.Unity" Version="6.0.224.925" />
    <PackageReference Include="Xm.UserInventory.Dto.Unity" Version="6.0.211.193" />
    <PackageReference Include="Xm.UserRewards.Dto.Unity" Version="6.0.224.877" />
    <PackageReference Include="Xm.Gameplays.Dto.Unity" Version="6.0.254.899" />
    <PackageReference Include="Xm.Contracts.SourceGeneration.Unity" Version="6.0.204.259">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Xm.Dev" Version="1.0.185.564">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Xm.UserProfiles.UserRecords.Dto.Unity" Version="6.0.207.517" />
    <PackageReference Include="Xm.GameActivities.Dto.Unity" Version="6.1.284.834" />
    <PackageReference Include="Xm.GameActivities.Attempts.Dto.Unity" Version="6.1.284.834" />
    <PackageReference Include="Xm.PromoOffers.Dto.Unity" Version="6.1.271.611" />
    <PackageReference Include="Xm.PromoOffers.GameActivities.Dto.Unity" Version="6.1.271.611" />
    <PackageReference Include="Xm.Advertisement.GameActivities.Dto.Unity" Version="6.1.270.267" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TriviaScapes.AppEvents.ClientIntegration.Contracts\TriviaScapes.AppEvents.ClientIntegration.Contracts.csproj" />
    <ProjectReference Include="..\TriviaScapes.ClientIntegration.Contracts\TriviaScapes.ClientIntegration.Contracts.csproj" />
    <ProjectReference Include="..\TriviaScapes.Competitions.ClientIntegration.Contracts\TriviaScapes.Competitions.ClientIntegration.Contracts.csproj" />
    <ProjectReference Include="..\TriviaScapes.Notifications.ClientIntegration.Contracts\TriviaScapes.Notifications.ClientIntegration.Contracts.csproj" />
    <ProjectReference Include="..\TriviaScapes.QuestionDelivery.ClientIntegration.Contracts\TriviaScapes.QuestionDelivery.ClientIntegration.Contracts.csproj" />
    <ProjectReference Include="..\TriviaScapes.Telemetry.ClientIntegration.Contracts\TriviaScapes.Telemetry.ClientIntegration.Contracts.csproj" />
  </ItemGroup>

</Project>
