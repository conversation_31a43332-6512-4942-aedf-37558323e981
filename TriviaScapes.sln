
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.1.32228.430
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TriviaScapes.Endpoint", "TriviaScapes.Endpoint\TriviaScapes.Endpoint.csproj", "{02330C31-C74B-4097-AC83-8E3768385FD6}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = ".nuget", ".nuget", "{EF60084F-6BD9-4905-8199-D7A68CA5195C}"
	ProjectSection(SolutionItems) = preProject
		.nuget\NuGet.Config = .nuget\NuGet.Config
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Build", "Build", "{61337B4C-D066-4FB9-B650-A45ABB306661}"
	ProjectSection(SolutionItems) = preProject
		.gitlab-ci.yml = .gitlab-ci.yml
		Build.Dockerfile = Build.Dockerfile
		Deploy.Dockerfile = Deploy.Dockerfile
		UnityDto.Dockerfile = UnityDto.Dockerfile
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TriviaScapes.ClientIntegration.Contracts", "TriviaScapes.ClientIntegration.Contracts\TriviaScapes.ClientIntegration.Contracts.csproj", "{F4D89220-0C35-4324-997F-0782AD0BE3C7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TriviaScapes.Dto.Unity", "TriviaScapes.Dto.Unity\TriviaScapes.Dto.Unity.csproj", "{3E38085E-2408-49D6-B3CE-16C9A51B0B96}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TriviaScapes.Progresses.Endpoint", "TriviaScapes.Progresses.Endpoint\TriviaScapes.Progresses.Endpoint.csproj", "{E3203B33-0314-4AC0-9447-E33903CD2A31}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TriviaScapes.Telemetry.Actions.Endpoint", "TriviaScapes.Telemetry.Actions.Endpoint\TriviaScapes.Telemetry.Actions.Endpoint.csproj", "{E0BB70F2-392B-48C8-A61D-15F2AD535990}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TriviaScapes.Telemetry.ClientIntegration.Contracts", "TriviaScapes.Telemetry.ClientIntegration.Contracts\TriviaScapes.Telemetry.ClientIntegration.Contracts.csproj", "{27377134-40D8-44AD-B670-ACC1888FFE10}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Telemetry", "Telemetry", "{781A911A-73ED-43D3-841B-109A433A0B65}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "QuestionDelivery", "QuestionDelivery", "{FF6D4BB3-9FCF-49A2-B74D-A97EEFD93F1E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TriviaScapes.QuestionDelivery.ClientIntegration.Contracts", "TriviaScapes.QuestionDelivery.ClientIntegration.Contracts\TriviaScapes.QuestionDelivery.ClientIntegration.Contracts.csproj", "{CDCEE1C9-AF39-447B-A3D0-DA96B31D28F4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TriviaScapes.QuestionDelivery.Endpoint", "TriviaScapes.QuestionDelivery.Endpoint\TriviaScapes.QuestionDelivery.Endpoint.csproj", "{06A32A37-60B5-493D-BCB1-939B1EFA07F4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TriviaScapes.QuestionDelivery.Presets", "TriviaScapes.QuestionDelivery.Presets\TriviaScapes.QuestionDelivery.Presets.csproj", "{C9B896DB-7AB7-40A5-A1BC-85AF1A3E284C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TriviaScapes.Dto", "TriviaScapes.Dto\TriviaScapes.Dto.csproj", "{ABF99966-F6FA-4001-8173-B3681785BBDD}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Notifications", "Notifications", "{6265D196-D50E-4BE5-9024-2ECEFA4E85E5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TriviaScapes.Notifications.ClientIntegration.Contracts", "TriviaScapes.Notifications.ClientIntegration.Contracts\TriviaScapes.Notifications.ClientIntegration.Contracts.csproj", "{73A7E477-B0AE-4FA3-BC17-69EED1B3E69F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TriviaScapes.Notifications.Scheduler", "TriviaScapes.Notifications.Scheduler\TriviaScapes.Notifications.Scheduler.csproj", "{AD6F059E-D834-4478-AD01-0CA296786F9F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "AppEvents", "AppEvents", "{B2BB382F-74C2-4C12-9011-49C39F5EA820}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TriviaScapes.AppEvents.Endpoint", "TriviaScapes.AppEvents.Endpoint\TriviaScapes.AppEvents.Endpoint.csproj", "{DC7F80FC-438A-4C8A-97B7-5EDF0A1A5E27}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TriviaScapes.AppEvents.ClientIntegration.Contracts", "TriviaScapes.AppEvents.ClientIntegration.Contracts\TriviaScapes.AppEvents.ClientIntegration.Contracts.csproj", "{361847F0-D47D-4E4A-BDC5-2186C7168437}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Competitions", "Competitions", "{2148B0D9-B35F-469C-B491-5CF2E4803206}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TriviaScapes.Competitions.ClientIntegration.Contracts", "TriviaScapes.Competitions.ClientIntegration.Contracts\TriviaScapes.Competitions.ClientIntegration.Contracts.csproj", "{E54AC566-E7A9-443A-A7ED-36DB01C286C1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TriviaScapes.Competitions.Endpoint", "TriviaScapes.Competitions.Endpoint\TriviaScapes.Competitions.Endpoint.csproj", "{D1E4C7FE-E613-4DEA-B7CE-AE4319992F95}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Experimental|Any CPU = Experimental|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{02330C31-C74B-4097-AC83-8E3768385FD6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{02330C31-C74B-4097-AC83-8E3768385FD6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{02330C31-C74B-4097-AC83-8E3768385FD6}.Experimental|Any CPU.ActiveCfg = Release|Any CPU
		{02330C31-C74B-4097-AC83-8E3768385FD6}.Experimental|Any CPU.Build.0 = Release|Any CPU
		{02330C31-C74B-4097-AC83-8E3768385FD6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{02330C31-C74B-4097-AC83-8E3768385FD6}.Release|Any CPU.Build.0 = Release|Any CPU
		{F4D89220-0C35-4324-997F-0782AD0BE3C7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F4D89220-0C35-4324-997F-0782AD0BE3C7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F4D89220-0C35-4324-997F-0782AD0BE3C7}.Experimental|Any CPU.ActiveCfg = Release|Any CPU
		{F4D89220-0C35-4324-997F-0782AD0BE3C7}.Experimental|Any CPU.Build.0 = Release|Any CPU
		{F4D89220-0C35-4324-997F-0782AD0BE3C7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F4D89220-0C35-4324-997F-0782AD0BE3C7}.Release|Any CPU.Build.0 = Release|Any CPU
		{3E38085E-2408-49D6-B3CE-16C9A51B0B96}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3E38085E-2408-49D6-B3CE-16C9A51B0B96}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3E38085E-2408-49D6-B3CE-16C9A51B0B96}.Experimental|Any CPU.ActiveCfg = Release|Any CPU
		{3E38085E-2408-49D6-B3CE-16C9A51B0B96}.Experimental|Any CPU.Build.0 = Release|Any CPU
		{3E38085E-2408-49D6-B3CE-16C9A51B0B96}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3E38085E-2408-49D6-B3CE-16C9A51B0B96}.Release|Any CPU.Build.0 = Release|Any CPU
		{E3203B33-0314-4AC0-9447-E33903CD2A31}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E3203B33-0314-4AC0-9447-E33903CD2A31}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E3203B33-0314-4AC0-9447-E33903CD2A31}.Experimental|Any CPU.ActiveCfg = Release|Any CPU
		{E3203B33-0314-4AC0-9447-E33903CD2A31}.Experimental|Any CPU.Build.0 = Release|Any CPU
		{E3203B33-0314-4AC0-9447-E33903CD2A31}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E3203B33-0314-4AC0-9447-E33903CD2A31}.Release|Any CPU.Build.0 = Release|Any CPU
		{E0BB70F2-392B-48C8-A61D-15F2AD535990}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E0BB70F2-392B-48C8-A61D-15F2AD535990}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E0BB70F2-392B-48C8-A61D-15F2AD535990}.Experimental|Any CPU.ActiveCfg = Release|Any CPU
		{E0BB70F2-392B-48C8-A61D-15F2AD535990}.Experimental|Any CPU.Build.0 = Release|Any CPU
		{E0BB70F2-392B-48C8-A61D-15F2AD535990}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E0BB70F2-392B-48C8-A61D-15F2AD535990}.Release|Any CPU.Build.0 = Release|Any CPU
		{27377134-40D8-44AD-B670-ACC1888FFE10}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{27377134-40D8-44AD-B670-ACC1888FFE10}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{27377134-40D8-44AD-B670-ACC1888FFE10}.Experimental|Any CPU.ActiveCfg = Release|Any CPU
		{27377134-40D8-44AD-B670-ACC1888FFE10}.Experimental|Any CPU.Build.0 = Release|Any CPU
		{27377134-40D8-44AD-B670-ACC1888FFE10}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{27377134-40D8-44AD-B670-ACC1888FFE10}.Release|Any CPU.Build.0 = Release|Any CPU
		{CDCEE1C9-AF39-447B-A3D0-DA96B31D28F4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CDCEE1C9-AF39-447B-A3D0-DA96B31D28F4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CDCEE1C9-AF39-447B-A3D0-DA96B31D28F4}.Experimental|Any CPU.ActiveCfg = Release|Any CPU
		{CDCEE1C9-AF39-447B-A3D0-DA96B31D28F4}.Experimental|Any CPU.Build.0 = Release|Any CPU
		{CDCEE1C9-AF39-447B-A3D0-DA96B31D28F4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CDCEE1C9-AF39-447B-A3D0-DA96B31D28F4}.Release|Any CPU.Build.0 = Release|Any CPU
		{06A32A37-60B5-493D-BCB1-939B1EFA07F4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{06A32A37-60B5-493D-BCB1-939B1EFA07F4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{06A32A37-60B5-493D-BCB1-939B1EFA07F4}.Experimental|Any CPU.ActiveCfg = Experimental|Any CPU
		{06A32A37-60B5-493D-BCB1-939B1EFA07F4}.Experimental|Any CPU.Build.0 = Experimental|Any CPU
		{06A32A37-60B5-493D-BCB1-939B1EFA07F4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{06A32A37-60B5-493D-BCB1-939B1EFA07F4}.Release|Any CPU.Build.0 = Release|Any CPU
		{C9B896DB-7AB7-40A5-A1BC-85AF1A3E284C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C9B896DB-7AB7-40A5-A1BC-85AF1A3E284C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C9B896DB-7AB7-40A5-A1BC-85AF1A3E284C}.Experimental|Any CPU.ActiveCfg = Release|Any CPU
		{C9B896DB-7AB7-40A5-A1BC-85AF1A3E284C}.Experimental|Any CPU.Build.0 = Release|Any CPU
		{C9B896DB-7AB7-40A5-A1BC-85AF1A3E284C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C9B896DB-7AB7-40A5-A1BC-85AF1A3E284C}.Release|Any CPU.Build.0 = Release|Any CPU
		{ABF99966-F6FA-4001-8173-B3681785BBDD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ABF99966-F6FA-4001-8173-B3681785BBDD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ABF99966-F6FA-4001-8173-B3681785BBDD}.Experimental|Any CPU.ActiveCfg = Release|Any CPU
		{ABF99966-F6FA-4001-8173-B3681785BBDD}.Experimental|Any CPU.Build.0 = Release|Any CPU
		{ABF99966-F6FA-4001-8173-B3681785BBDD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ABF99966-F6FA-4001-8173-B3681785BBDD}.Release|Any CPU.Build.0 = Release|Any CPU
		{73A7E477-B0AE-4FA3-BC17-69EED1B3E69F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{73A7E477-B0AE-4FA3-BC17-69EED1B3E69F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{73A7E477-B0AE-4FA3-BC17-69EED1B3E69F}.Experimental|Any CPU.ActiveCfg = Release|Any CPU
		{73A7E477-B0AE-4FA3-BC17-69EED1B3E69F}.Experimental|Any CPU.Build.0 = Release|Any CPU
		{73A7E477-B0AE-4FA3-BC17-69EED1B3E69F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{73A7E477-B0AE-4FA3-BC17-69EED1B3E69F}.Release|Any CPU.Build.0 = Release|Any CPU
		{AD6F059E-D834-4478-AD01-0CA296786F9F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AD6F059E-D834-4478-AD01-0CA296786F9F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AD6F059E-D834-4478-AD01-0CA296786F9F}.Experimental|Any CPU.ActiveCfg = Release|Any CPU
		{AD6F059E-D834-4478-AD01-0CA296786F9F}.Experimental|Any CPU.Build.0 = Release|Any CPU
		{AD6F059E-D834-4478-AD01-0CA296786F9F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AD6F059E-D834-4478-AD01-0CA296786F9F}.Release|Any CPU.Build.0 = Release|Any CPU
		{DC7F80FC-438A-4C8A-97B7-5EDF0A1A5E27}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC7F80FC-438A-4C8A-97B7-5EDF0A1A5E27}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC7F80FC-438A-4C8A-97B7-5EDF0A1A5E27}.Experimental|Any CPU.ActiveCfg = Release|Any CPU
		{DC7F80FC-438A-4C8A-97B7-5EDF0A1A5E27}.Experimental|Any CPU.Build.0 = Release|Any CPU
		{DC7F80FC-438A-4C8A-97B7-5EDF0A1A5E27}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC7F80FC-438A-4C8A-97B7-5EDF0A1A5E27}.Release|Any CPU.Build.0 = Release|Any CPU
		{361847F0-D47D-4E4A-BDC5-2186C7168437}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{361847F0-D47D-4E4A-BDC5-2186C7168437}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{361847F0-D47D-4E4A-BDC5-2186C7168437}.Experimental|Any CPU.ActiveCfg = Release|Any CPU
		{361847F0-D47D-4E4A-BDC5-2186C7168437}.Experimental|Any CPU.Build.0 = Release|Any CPU
		{361847F0-D47D-4E4A-BDC5-2186C7168437}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{361847F0-D47D-4E4A-BDC5-2186C7168437}.Release|Any CPU.Build.0 = Release|Any CPU
		{E54AC566-E7A9-443A-A7ED-36DB01C286C1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E54AC566-E7A9-443A-A7ED-36DB01C286C1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E54AC566-E7A9-443A-A7ED-36DB01C286C1}.Experimental|Any CPU.ActiveCfg = Release|Any CPU
		{E54AC566-E7A9-443A-A7ED-36DB01C286C1}.Experimental|Any CPU.Build.0 = Release|Any CPU
		{E54AC566-E7A9-443A-A7ED-36DB01C286C1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E54AC566-E7A9-443A-A7ED-36DB01C286C1}.Release|Any CPU.Build.0 = Release|Any CPU
		{D1E4C7FE-E613-4DEA-B7CE-AE4319992F95}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D1E4C7FE-E613-4DEA-B7CE-AE4319992F95}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D1E4C7FE-E613-4DEA-B7CE-AE4319992F95}.Experimental|Any CPU.ActiveCfg = Release|Any CPU
		{D1E4C7FE-E613-4DEA-B7CE-AE4319992F95}.Experimental|Any CPU.Build.0 = Release|Any CPU
		{D1E4C7FE-E613-4DEA-B7CE-AE4319992F95}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D1E4C7FE-E613-4DEA-B7CE-AE4319992F95}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{E0BB70F2-392B-48C8-A61D-15F2AD535990} = {781A911A-73ED-43D3-841B-109A433A0B65}
		{27377134-40D8-44AD-B670-ACC1888FFE10} = {781A911A-73ED-43D3-841B-109A433A0B65}
		{CDCEE1C9-AF39-447B-A3D0-DA96B31D28F4} = {FF6D4BB3-9FCF-49A2-B74D-A97EEFD93F1E}
		{06A32A37-60B5-493D-BCB1-939B1EFA07F4} = {FF6D4BB3-9FCF-49A2-B74D-A97EEFD93F1E}
		{C9B896DB-7AB7-40A5-A1BC-85AF1A3E284C} = {FF6D4BB3-9FCF-49A2-B74D-A97EEFD93F1E}
		{73A7E477-B0AE-4FA3-BC17-69EED1B3E69F} = {6265D196-D50E-4BE5-9024-2ECEFA4E85E5}
		{AD6F059E-D834-4478-AD01-0CA296786F9F} = {6265D196-D50E-4BE5-9024-2ECEFA4E85E5}
		{DC7F80FC-438A-4C8A-97B7-5EDF0A1A5E27} = {B2BB382F-74C2-4C12-9011-49C39F5EA820}
		{361847F0-D47D-4E4A-BDC5-2186C7168437} = {B2BB382F-74C2-4C12-9011-49C39F5EA820}
		{E54AC566-E7A9-443A-A7ED-36DB01C286C1} = {2148B0D9-B35F-469C-B491-5CF2E4803206}
		{D1E4C7FE-E613-4DEA-B7CE-AE4319992F95} = {2148B0D9-B35F-469C-B491-5CF2E4803206}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {6C107518-A04F-4D15-9E47-A46DF0132096}
	EndGlobalSection
EndGlobal
