<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Xm.Contracts.SourceGeneration.Net" Version="6.0.204.259">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Xm.Dev" Version="1.0.185.564">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Xm.Notifications.Scheduler.Core" Version="6.0.219.446" />
        <PackageReference Include="Xm.Notifications.Scheduler.RazorPages" Version="6.0.219.446" />
        <PackageReference Include="Xm.Notifications.Scheduler.Tasks" Version="6.0.219.446" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\TriviaScapes.Notifications.ClientIntegration.Contracts\TriviaScapes.Notifications.ClientIntegration.Contracts.csproj" />
    </ItemGroup>

</Project>
