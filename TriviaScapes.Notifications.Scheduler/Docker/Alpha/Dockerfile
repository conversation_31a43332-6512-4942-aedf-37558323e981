ARG project="TriviaScapes.Notifications.Scheduler"
FROM lab.xmonetize.net:5050/infrastructure/dotnet-docker/aspnet-build:6.0 AS build

FROM lab.xmonetize.net:5050/infrastructure/dotnet-docker/aspnet-utc:6.0 AS base

WORKDIR /app

COPY --from=build /app .
RUN rm -rf /app/Environments/Development
RUN rm -rf /app/Environments/Beta
RUN rm -rf /app/Environments/Production

ENV ASPNETCORE_ENVIRONMENT=Alpha
ENTRYPOINT ["dotnet", "TriviaScapes.Notifications.Scheduler.dll"]