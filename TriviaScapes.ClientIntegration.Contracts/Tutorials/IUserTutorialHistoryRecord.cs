using System.Collections.Generic;
using Xm.Contracts.Abstractions;
using Xm.UserRecords.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Tutorials
{
    [XmUserRecordName("tutorials")]
    [SerializableContract("ruth")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface IUserTutorialHistoryRecord
    {
        [SerializableContractProperty("i", 0)]
        IReadOnlyDictionary<string, IUserTutorialHistoryItemClientIntegration> Items { get; }
    }
}