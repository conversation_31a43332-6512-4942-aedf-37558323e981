using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Tutorials
{
    [SerializableContract("ciuthi")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface IUserTutorialHistoryItemClientIntegration
    {
        [SerializableContractProperty("s", 0)]
        long ShownAt { get; }
    }
}