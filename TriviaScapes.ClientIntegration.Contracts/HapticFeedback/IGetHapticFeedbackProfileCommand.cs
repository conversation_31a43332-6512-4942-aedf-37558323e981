using Xm.Commands.Abstractions;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.HapticFeedback
{
    [SerializableContract("cghfp")]
    public interface IGetHapticFeedbackProfileCommand : IXmCommand
    {
        [SerializableContractProperty("pn", 0)]
        string ProfileName { get; }

        [SerializableContractProperty("cv", 1)]
        string CurrentVersion { get; }
    }
}
