using Xm.AppSettings.Abstractions;
using Xm.Commands.Abstractions;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.HapticFeedback
{
    [SerializableContract("cghfpr")]
    public interface IGetHapticFeedbackProfileCommandResult : IXmCommandResult
    {
        [SerializableContractProperty("p", 0)]
        IXmAppSettings<IHapticFeedbackProfileClientIntegration> Profile { get; }
    }
}
