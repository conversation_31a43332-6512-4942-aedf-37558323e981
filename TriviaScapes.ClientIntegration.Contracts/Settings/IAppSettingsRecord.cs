using Xm.Contracts.Abstractions;
using Xm.UserRecords.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Settings
{
    [XmUserRecordName("settings")]
    [SerializableContract("raset")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface IAppSettingsRecord
    {
        [SerializableContractProperty("imm", 0)]
        bool IsMusicMuted { get; }
        
        [SerializableContractProperty("ism", 1)]
        bool IsSoundMuted { get; }

        [SerializableContractProperty("ihfe", 2)]
        bool IsHapticFeedbackEnabled { get; }
    }
}