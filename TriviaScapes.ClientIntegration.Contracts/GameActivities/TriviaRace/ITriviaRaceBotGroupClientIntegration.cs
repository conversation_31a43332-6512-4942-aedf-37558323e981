using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.GameActivities.TriviaRace
{
    [SerializableContract("triviaracecibg")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface ITriviaRaceBotGroupClientIntegration
    {
        [SerializableContractProperty("c", 0)]
        int Count { get; }

        [SerializableContractProperty("minp", 1)]
        int MinProgress { get; }

        [SerializableContractProperty("maxp", 2)]
        int MaxProgress { get; }
    }
}