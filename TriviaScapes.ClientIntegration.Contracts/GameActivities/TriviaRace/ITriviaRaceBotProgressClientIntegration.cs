using System;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.GameActivities.TriviaRace
{
    [SerializableContract("triviaracecibprog")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface ITriviaRaceBotProgressClientIntegration
    {
        [SerializableContractProperty("i", 0)]
        Guid Id { get; }

        [SerializableContractProperty("au", 1)]
        string AvatarUrl { get; }

        [SerializableContractProperty("m", 2)]
        int Metric { get; }

        [SerializableContractProperty("gi", 3)]
        int GroupId { get; }

        [SerializableContractProperty("p", 4)]
        int Place { get; }

        [SerializableContractProperty("if", 5)]
        bool IsFinished { get; }
    }
}