using Xm.Contracts.Abstractions;
using Xm.GameActivities.ClientIntegration.Contracts;

namespace TriviaScapes.ClientIntegration.Contracts.GameActivities.TriviaRace
{
    [SerializableContract("triviaracecip")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface ITriviaRaceParametersClientIntegration : IXmGameActivityParametersClientIntegration
    {
        [SerializableContractProperty("fqc", 0)]
        int FinishQuestionsCount { get; }

        [SerializableContractProperty("r", 1)]
        ITriviaRaceRewardSetClientIntegration Rewards { get; }

        [SerializableContractProperty("b", 2)]
        ITriviaRaceBotParametersClientIntegration Bots { get; }

        [SerializableContractProperty("u", 3)]
        ITriviaRaceUIParametersClientIntegration Ui { get; }
    }
}