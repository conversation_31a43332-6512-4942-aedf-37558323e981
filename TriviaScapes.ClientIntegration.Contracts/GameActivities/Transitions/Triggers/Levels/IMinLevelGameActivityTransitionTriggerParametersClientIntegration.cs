using Xm.Contracts.Abstractions;
using Xm.GameActivities.ClientIntegration.Contracts.Transitions.Triggers;

namespace TriviaScapes.ClientIntegration.Contracts.GameActivities.Transitions.Triggers.Levels
{
    [SerializableContract("gactciminlvlttp")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface IMinLevelGameActivityTransitionTriggerParametersClientIntegration : IXmGameActivityTransitionTriggerParametersClientIntegration
    {
        [SerializableContractProperty("b", 0)]
        GameActivityLevelTransitionTriggerBasis Basis { get; }

        [SerializableContractProperty("lvl", 1)]
        int Level { get; }
    }
}