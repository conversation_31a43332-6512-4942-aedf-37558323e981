using Xm.ConfigExtensions.ClientIntegration.Contracts.Conditions;
using Xm.Contracts.Abstractions;
using Xm.GameActivities.ClientIntegration.Contracts.Transitions.Triggers;

namespace TriviaScapes.ClientIntegration.Contracts.GameActivities.Transitions.Triggers.Levels
{
    [SerializableContract("gactcilvlcttp")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface ILevelConditionGameActivityTransitionTriggerParametersClientIntegration : IXmGameActivityTransitionTriggerParametersClientIntegration
    {
        [SerializableContractProperty("b", 0)]
        GameActivityLevelTransitionTriggerBasis Basis { get; }

        [SerializableContractProperty("c", 1)]
        IXmIntegerConditionClientIntegration Condition { get; }
    }
}