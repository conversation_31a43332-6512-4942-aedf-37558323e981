using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Presets.SkipIt
{
    [SerializableContract("ciskipitset")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface ISkipItSettingsClientIntegration
    {
        [SerializableContractProperty("aal", 0)]
        int AvailableAfterLevel { get; }

        [SerializableContractProperty("ic", 1)]
        int InitialCount { get; }
    }
}