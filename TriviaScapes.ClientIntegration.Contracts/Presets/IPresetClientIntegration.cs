using System;
using TriviaScapes.ClientIntegration.Contracts.BrainScore;
using TriviaScapes.ClientIntegration.Contracts.GameActivities.Icons;
using TriviaScapes.ClientIntegration.Contracts.Presets.Gameplay;
using TriviaScapes.ClientIntegration.Contracts.Presets.GameResources;
using TriviaScapes.ClientIntegration.Contracts.Presets.LevelRewardDesign;
using TriviaScapes.ClientIntegration.Contracts.Presets.MainMenu;
using TriviaScapes.ClientIntegration.Contracts.Presets.ReviewManager;
using TriviaScapes.ClientIntegration.Contracts.Presets.SkipIt;
using TriviaScapes.ClientIntegration.Contracts.Presets.Tutorials;
using TriviaScapes.ClientIntegration.Contracts.Presets.UI;
using TriviaScapes.ClientIntegration.Contracts.Presets.UserBonuses;
using TriviaScapes.ClientIntegration.Contracts.Presets.UserRewards;
using Xm.Contracts.Abstractions;
using Xm.ExperimentalFeatures.ClientIntegration.Contracts.References;
using Xm.GameActivities.ClientIntegration.Contracts.Scenarios;
using Xm.Presets.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Presets
{
    [SerializableContract("cip")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface IPresetClientIntegration
    {
        [SerializableContractProperty("i", 0)]
        IXmPresetIdentifier Identifier { get; }

        [SerializableContractProperty("rdp", 1)]
        string RateDialogProfile { get; }

        [SerializableContractProperty("gr", 2)]
        IGameResourcesPresetClientIntegration GameResources { get; }

        [SerializableContractProperty("adp", 3)]
        string AdDisplayProfile { get; }

        [SerializableContractProperty("s", 4)]
        string Store { get; }

        [SerializableContractProperty("ub", 5)]
        IUserBonusesPresetClientIntegration UserBonuses { get; }

        [SerializableContractProperty("lmp", 6)]
        [Obsolete("Since " + nameof(AppReleaseVersions.NewGameplayStructure))]
        string Legacyv1LevelMapProfile { get; }

        [SerializableContractProperty("urwd", 7)]
        [Obsolete("Since " + nameof(AppReleaseVersions.NewGameplayStructure))]
        IUserRewardsPresetClientIntegration Legacyv1UserRewards { get; }

        [SerializableContractProperty("ldp", 8)]
        string CampaignLevelDesignProfile { get; }

        [SerializableContractProperty("g", 9)]
        IGameplayPresetClientIntegration Gameplay { get; }

        [SerializableContractProperty("adpg", 10)]
        string AdPresetGroup { get; }

        [SerializableContractProperty("t", 11)]
        IUserTutorialsPresetClientIntegration Tutorials { get; }

        [SerializableContractProperty("loct", 12)]
        string LocalizationTest { get; }

        [SerializableContractProperty("lp", 13)]
        string LeaderboardProfile { get; }

        [SerializableContractProperty("ap", 14)]
        string AchievementProfile { get; }

        [SerializableContractProperty("comprewp", 15)]
        string CompetitionRewardProfile { get; }

        [SerializableContractProperty("mm", 16)]
        IMainMenuPresetClientIntegration MainMenu { get; set; }

        [SerializableContractProperty("uabp", 17)]
        string AchievementBalanceProfile { get; }

        [Obsolete("Since " + nameof(AppReleaseVersions.FinalizeRateDialog))]
        [SerializableContractProperty("rdd", 18)]
        IRateDialogDesignPresetClientIntegration RateDialogDesign { get; }

        [SerializableContractProperty("ui", 19)]
        IUIPresetClientIntegration UI { get; }

        [SerializableContractProperty("pop", 20)]
        [Obsolete("Since " + nameof(AppReleaseVersions.PromoOffers_v2))]
        string Legacyv1PromoOfferProfile { get; }

        [SerializableContractProperty("povde", 21)]
        [Obsolete("Since " + nameof(AppReleaseVersions.PromoOffers_v2))]
        string Legacyv1PromoOfferViewDesignExperiment { get; }

        [SerializableContractProperty("icp", 22)]
        string ImageCollectionProfile { get; }

        [SerializableContractProperty("gcp", 23)]
        [Obsolete("Since " + nameof(AppReleaseVersions.NewGameplayStructure))]
        string Legacyv1GameChallengeProfile { get; }

        [SerializableContractProperty("gcadp", 24)]
        string ImageCollectionAdDisplayProfile { get; }

        [SerializableContractProperty("lrd", 25)]
        ILevelRewardDesignPresetClientIntegration LevelRewardDesign { get; }

        [SerializableContractProperty("expf", 26)]
        IXmExperimentalFeatureReferenceListClientIntegration ExperimentalFeatures { get; }

        [SerializableContractProperty("skipit", 27)]
        ISkipItSettingsClientIntegration SkipIt { get; }

        [SerializableContractProperty("blgcp", 28)]
        [Obsolete("Since " + nameof(AppReleaseVersions.NewGameplayStructure))]
        string Legacyv1BonusLevelGameChallengeSequence { get; }

        [SerializableContractProperty("bladp", 29)]
        string BonusLevelAdDisplayProfile { get; }

        [Obsolete("Since " + nameof(AppReleaseVersions.OnboardingBeforePresetInit))]
        [SerializableContractProperty("op", 30)]
        string OnboardingProfile { get; }

        [SerializableContractProperty("gactscen", 31)]
        IXmGameActivityScenarioReferenceListClientIntegration GameActivityScenarios { get; }

        [SerializableContractProperty("brains", 32)]
        IBrainScoreSettingsClientIntegration BrainScore { get; }

        [SerializableContractProperty("hfp", 33)]
        string HapticFeedbackProfile { get; }

        [SerializableContractProperty("campp", 34)]
        string CampaignProfile { get; }

        [SerializableContractProperty("gactadp", 35)]
        string GameActivityAdDisplayProfile { get; }

        [SerializableContractProperty("poffvde", 36)]
        string PromoOfferViewDesignExperiment { get; }

        [SerializableContractProperty("gactics", 37)]
        IGameActivityIconSettingsClientIntegration GameActivityIconSettings { get; set; }
    }
}