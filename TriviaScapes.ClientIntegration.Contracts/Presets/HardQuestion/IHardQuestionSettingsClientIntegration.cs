using System;
using System.Collections.Generic;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Presets.HardQuestion
{
    [SerializableContract("cihqs")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface IHardQuestionSettingsClientIntegration
    {
        [SerializableContractProperty("afl", 0)]
        int AvailableFromLevel { get; }

        [Obsolete("Since " + nameof(AppReleaseVersions.PromoOffers_v2))]
        [SerializableContractProperty("kpc", 1)]
        int KeysPerChapter { get; }

        [SerializableContractProperty("tkc", 2)]
        int TotalKeysCount { get; }

        [SerializableContractProperty("vhq", 3)]
        bool VisualizeHardQuestion { get; }

        [SerializableContractProperty("r", 4)]
        IReadOnlyList<IHardQuestionRewardSettingsClientIntegration> Rewards { get; }
    }
}