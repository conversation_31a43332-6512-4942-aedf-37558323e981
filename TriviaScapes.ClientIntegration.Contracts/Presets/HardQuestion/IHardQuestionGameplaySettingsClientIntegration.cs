using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Presets.HardQuestion
{
    [SerializableContract("cihqgs")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface IHardQuestionGameplaySettingsClientIntegration
    {
        [SerializableContractProperty("kpc", 0)]
        int KeysPerChapter { get; }
    }
}