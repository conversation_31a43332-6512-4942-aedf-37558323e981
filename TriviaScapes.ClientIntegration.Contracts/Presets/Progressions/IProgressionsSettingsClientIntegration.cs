using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Presets.Progressions
{
    [SerializableContract("ciprgss")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface IProgressionsSettingsClientIntegration
    {
        [SerializableContractProperty("ltr", 0)]
        IProgressionSettingsClientIntegration Letters { get; }
    }
}