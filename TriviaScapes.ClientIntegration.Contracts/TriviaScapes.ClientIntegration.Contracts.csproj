<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>netstandard2.0</TargetFramework>
    </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Xm.Advertisement.ClientIntegration.Contracts" Version="6.1.270.267" />
    <PackageReference Include="Xm.Billing.ClientIntegration.Contracts" Version="6.1.286.840" />
    <PackageReference Include="Xm.Billing.UserRewards.ClientIntegration.Contracts" Version="6.1.286.840" />
    <PackageReference Include="Xm.Cmp.ClientIntegration.Contracts" Version="6.0.203.110" />
    <PackageReference Include="Xm.ConfigExtensions.ClientIntegration.Contracts" Version="6.0.202.933" />
    <PackageReference Include="Xm.ExperimentalFeatures.ClientIntegration.Contracts" Version="6.1.256.347" />
    <PackageReference Include="Xm.GameGoals.ClientIntegration.Contracts" Version="6.0.239.270" />
    <PackageReference Include="Xm.GameLives.ClientIntegration.Contracts" Version="6.0.208.899" />
    <PackageReference Include="Xm.Gameplays.ClientIntegration.Contracts" Version="6.0.254.899" />
    <PackageReference Include="Xm.Language" Version="6.3.274.8" />
    <PackageReference Include="Xm.Presets.Commands.Abstractions" Version="6.4.273.367" />
    <PackageReference Include="Xm.PromoOffers.ClientIntegration.Contracts" Version="6.1.271.611" />
    <PackageReference Include="Xm.PromoOffers.GameActivities.ClientIntegration.Contracts" Version="6.1.271.611" />
    <PackageReference Include="Xm.UserAchievements.ClientIntegration.Contracts" Version="6.1.235.370" />
    <PackageReference Include="Xm.UserInventory.UserRecords.ClientIntegration.Contracts" Version="6.0.211.193" />
    <PackageReference Include="Xm.Users.Abstractions" Version="6.1.248.682" />
    <PackageReference Include="Xm.UserRewards.ClientIntegration.Contracts" Version="6.0.224.877" />
    <PackageReference Include="Xm.UserRecords.Commands.Abstractions" Version="6.0.271.231" />
    <PackageReference Include="Xm.AppSettings.Abstractions" Version="6.0.202.681" />
    <PackageReference Include="Xm.Commands.Abstractions" Version="6.0.231.917" />
    <PackageReference Include="Xm.Contracts.Abstractions" Version="6.0.204.259" />
    <PackageReference Include="Xm.LevelMaps.ClientIntegration.Contracts" Version="6.1.249.541" />
    <PackageReference Include="Xm.GameActivities.ClientIntegration.Contracts" Version="6.1.284.834" />
    <PackageReference Include="Xm.GameActivities.Attempts.ClientIntegration.Contracts" Version="6.1.284.834" />
    <PackageReference Include="Xm.Dev" Version="1.0.185.564">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
