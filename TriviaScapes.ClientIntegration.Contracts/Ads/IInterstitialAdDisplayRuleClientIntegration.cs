using System;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Ads
{
    [SerializableContract("ciiadr")]
    public interface IInterstitialAdDisplayRuleClientIntegration
    {
        [SerializableContractProperty("t", 0)]
        InterstitialAdDisplayRuleType Type { get; }

        [SerializableContractProperty("p", 1)]
        int Period { get; }

        [SerializableContractProperty("prelp", 2)]
        [Obsolete("Since " + nameof(AppReleaseVersions.RemoveAdPreloadingConditions))]
        int PreloadingPeriod { get; }

        [SerializableContractProperty("to", 3)]
        TimeSpan Timeout { get; }
    }
}