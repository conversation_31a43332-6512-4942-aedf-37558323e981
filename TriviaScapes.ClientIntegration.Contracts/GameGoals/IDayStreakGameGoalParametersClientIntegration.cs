using Xm.Contracts.Abstractions;
using Xm.GameGoals.ClientIntegration.Contracts.TargetValue;

namespace TriviaScapes.ClientIntegration.Contracts.GameGoals
{
    [SerializableContract("uacidsgp")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface IDayStreakGameGoalParametersClientIntegration : IXmTargetValueGameGoalParametersClientIntegration
    { }
}