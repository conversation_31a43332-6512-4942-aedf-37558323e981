using Xm.Billing.ClientIntegration.Contracts.Products;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Billing.Products
{
    [SerializableContract("ciqbllprodp")]
    public interface IQuizBlitzLocalLiveProductParametersClientIntegration : IXmInAppProductParametersClientIntegration
    {
        [SerializableContractProperty("rla", 0)]
        int RestoreLivesAmount { get; }
    }
}