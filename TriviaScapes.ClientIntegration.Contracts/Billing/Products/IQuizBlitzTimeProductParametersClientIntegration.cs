using Xm.Billing.ClientIntegration.Contracts.Products;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Billing.Products
{
    [SerializableContract("ciqbtprodp")]
    public interface IQuizBlitzTimeProductParametersClientIntegration : IXmInAppProductParametersClientIntegration
    {
        [SerializableContractProperty("rta", 0)]
        int RestoreTimeAmount { get; }
    }
}