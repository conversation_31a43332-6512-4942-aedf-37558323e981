using System;
using System.Collections.Generic;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Billing.Stores
{
    [SerializableContract("cis")]
    public interface IStoreClientIntegration
    {
        [SerializableContractProperty("sp", 1)]
        IReadOnlyList<IStoreProductClientIntegration> StoreProducts { get; }

        [SerializableContractProperty("lep", 2)]
        IStoreProductClientIntegration LevelEntranceProduct { get; }

        [SerializableContractProperty("op", 3)]
        IStoreProductClientIntegration OfferProduct { get; }

        [SerializableContractProperty("oopp", 4)]
        IStoreProductClientIntegration OfferOldPriceProduct { get; }

        [SerializableContractProperty("necp", 5)]
        IStoreProductClientIntegration NotEnoughCoinsProduct { get; }

        [SerializableContractProperty("nelp", 6)]
        IStoreProductClientIntegration NotEnoughLivesProduct { get; }

        [SerializableContractProperty("flelp", 7)]
        IStoreProductClientIntegration FailedLevelExtraLifeProduct { get; }

        [SerializableContractProperty("hp", 8)]
        IReadOnlyList<IStoreProductClientIntegration> HintProducts { get; }

        [Obsolete("Since " + nameof(AppReleaseVersions.NewGameplayStructure))]
        [SerializableContractProperty("gcqap", 9)]
        IStoreProductClientIntegration GameChallengeQuestionAttemptProduct { get; }

        [SerializableContractProperty("nesip", 10)]
        IStoreProductClientIntegration NotEnoughSkipItProduct { get; }

        [SerializableContractProperty("nehp", 11)]
        IReadOnlyList<IStoreProductClientIntegration> NotEnoughHintProducts { get; }

        [SerializableContractProperty("nelpl", 12)]
        IReadOnlyList<IStoreProductClientIntegration> NotEnoughLiveProductList { get; }

        [SerializableContractProperty("necpl", 13)]
        IReadOnlyList<IStoreProductClientIntegration> NotEnoughCoinProductList { get; }

        [SerializableContractProperty("nesipl", 14)]
        IReadOnlyList<IStoreProductClientIntegration> NotEnoughSkipItProductList { get; }

        [SerializableContractProperty("esbpl", 15)]
        IReadOnlyList<IStoreProductClientIntegration> ExpiredScoreBoostersProductList { get; }

        [SerializableContractProperty("elbpl", 16)]
        IReadOnlyList<IStoreProductClientIntegration> ExpiredLiveBoostersProductList { get; }

        [SerializableContractProperty("netp", 17)]
        IReadOnlyList<IStoreProductClientIntegration> NotEnoughTimeProducts { get; }

        [SerializableContractProperty("nellp", 18)]
        IReadOnlyList<IStoreProductClientIntegration> NotEnoughLocalLivesProducts { get; }
    }
}