using Xm.Contracts.Abstractions;
using Xm.PromoOffers.ClientIntegration.Contracts;

namespace TriviaScapes.ClientIntegration.Contracts.PromoOffers.InAppPurchases
{
    [SerializableContract("ciiapppoffprg")]
    public interface IInAppPurchasePromoOfferProgressClientIntegration: IXmPromoOfferProgressClientIntegration
    {
        [SerializableContractProperty("pc", 0)]
        int PurchasesCount { get; }
    }
}