using Xm.AppSettings.Abstractions;
using Xm.Commands.Abstractions;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Leaderboards
{
    [SerializableContract("cglpr")]
    public interface IGetLeaderboardProfileCommandResult : IXmCommandResult
    {
        [SerializableContractProperty("p", 0)]
        IXmAppSettings<ILeaderboardProfileClientIntegration> Profile { get; }
    }
}
