using Xm.Commands.Abstractions;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Leaderboards
{
    [SerializableContract("cglp")]
    public interface IGetLeaderboardProfileCommand : IXmCommand
    {
        [SerializableContractProperty("pn", 0)]
        string ProfileName { get; }

        [SerializableContractProperty("cv", 1)]
        string CurrentVersion { get; }
    }
}
