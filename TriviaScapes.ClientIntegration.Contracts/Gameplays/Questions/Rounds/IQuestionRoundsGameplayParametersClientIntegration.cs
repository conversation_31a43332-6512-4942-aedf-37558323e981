using System.Collections.Generic;
using TriviaScapes.ClientIntegration.Contracts.Inventory;
using Xm.Contracts.Abstractions;
using Xm.Gameplays.ClientIntegration.Contracts;

namespace TriviaScapes.ClientIntegration.Contracts.Gameplays.Questions.Rounds
{
    [SerializableContract("gplayciqroundp")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface IQuestionRoundsGameplayParametersClientIntegration : IXmGameplayParametersClientIntegration
    {
        [SerializableContractProperty("r", 0)]
        IReadOnlyList<IQuestionRoundClientIntegration> Rounds { get; }

        [SerializableContractProperty("mcsa", 1)]
        int MaxCategorySelectionAttempts { get; }

        [SerializableContractProperty("ah", 2)]
        IReadOnlyList<QuestionHintKind> AvailableHints { get; }

        [SerializableContractProperty("qatd", 3)]
        int QuestionAnswerTimerDuration { get; }

        [SerializableContractProperty("mt", 4)]
        IMotivationTextSettingsClientIntegration MotivationText { get; }

        [SerializableContractProperty("res", 5)]
        GlobalGameplayResourceKind Resource { get; }

        [SerializableContractProperty("compr", 6)]
        int CompletionReward { get; }
    }
}