using System;
using Xm.Contracts.Abstractions;
using Xm.Gameplays.ClientIntegration.Contracts;

namespace TriviaScapes.ClientIntegration.Contracts.Gameplays.Questions.Rush
{
    [SerializableContract("gplayciqrushp")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface IQuestionRushGameplayParametersClientIntegration : IXmGameplayParametersClientIntegration
    {
        [SerializableContractProperty("q", 0)]
        IQuestionSettingsClientIntegration Questions { get; }

        [SerializableContractProperty("res", 1)]
        GlobalGameplayResourceKind Resource { get; }

        [SerializableContractProperty("d", 2)]
        TimeSpan Duration { get; }

        [SerializableContractProperty("wap", 3)]
        TimeSpan WrongAnswerPenalty { get; }

        [SerializableContractProperty("respq", 4)]
        int ResourcePerQuestion { get; }

        [SerializableContractProperty("nqs", 5)]
        NextQuestionStrategy NextQuestionStrategy { get; }
    }
}