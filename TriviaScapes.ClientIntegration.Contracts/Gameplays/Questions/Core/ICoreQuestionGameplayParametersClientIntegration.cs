using System.Collections.Generic;
using TriviaScapes.ClientIntegration.Contracts.Inventory;
using TriviaScapes.ClientIntegration.Contracts.Presets.HardQuestion;
using Xm.Contracts.Abstractions;
using Xm.Gameplays.ClientIntegration.Contracts;

namespace TriviaScapes.ClientIntegration.Contracts.Gameplays.Questions.Core
{
    [SerializableContract("gplaycicoreqp")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface ICoreQuestionGameplayParametersClientIntegration : IXmGameplayParametersClientIntegration
    {
        [SerializableContractProperty("q", 0)]
        IQuestionSettingsClientIntegration Questions { get; }

        [SerializableContractProperty("fad", 1)]
        int FastAnswerDuration { get; }

        [SerializableContractProperty("ah", 2)]
        IReadOnlyList<QuestionHintKind> AvailableHints { get; }

        [SerializableContractProperty("mt", 3)]
        IMotivationTextSettingsClientIntegration MotivationText { get; }

        [SerializableContractProperty("res", 4)]
        GlobalGameplayResourceKind Resource { get; }

        [SerializableContractProperty("qr", 5)]
        int QuestionReward { get; }

        [SerializableContractProperty("far", 6)]
        int FastAnswerReward { get; }

        [SerializableContractProperty("lqr", 7)]
        int LettersQuestionReward { get; }

        [SerializableContractProperty("hq", 8)]
        IHardQuestionGameplaySettingsClientIntegration HardQuestion { get; }
    }
}