using System;
using Xm.Contracts.Abstractions;
using Xm.Gameplays.ClientIntegration.Contracts;

namespace TriviaScapes.ClientIntegration.Contracts.Gameplays.Questions.TriviaCasino
{
    [SerializableContract("gplaycitcasinop")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface ITriviaCasinoGameplayParametersClientIntegration : IXmGameplayParametersClientIntegration
    {
        [SerializableContractProperty("q", 0)]
        IQuestionSettingsClientIntegration Questions { get; }

        [SerializableContractProperty("d", 1)]
        TimeSpan Duration { get; }

        [SerializableContractProperty("wap", 2)]
        TimeSpan WrongAnswerPenalty { get; }

        [SerializableContractProperty("cnspc", 3)]
        int CoinsPerChip { get; }
    }
}