using System.Collections.Generic;
using TriviaScapes.ClientIntegration.Contracts.Inventory;
using Xm.Contracts.Abstractions;
using Xm.Gameplays.ClientIntegration.Contracts;

namespace TriviaScapes.ClientIntegration.Contracts.Gameplays.Questions.Survival
{
    [SerializableContract("gplaycisurvivalp")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface ISurvivalQuizGameplayParametersClientIntegration : IXmGameplayParametersClientIntegration
    {
        [SerializableContractProperty("qd", 0)]
        float QuestionDuration { get; }

        [SerializableContractProperty("b", 1)]
        ISurvivalQuizBotSettingsClientIntegration Bots { get; }

        [SerializableContractProperty("r", 2)]
        int Reward { get; }

        [SerializableContractProperty("q", 3)]
        IQuestionSettingsClientIntegration Questions { get; }

        [SerializableContractProperty("ah", 4)]
        IReadOnlyList<QuestionHintKind> AvailableHints { get; }

        [SerializableContractProperty("mt", 5)]
        IMotivationTextSettingsClientIntegration MotivationText { get; }

        [SerializableContractProperty("md", 6)]
        float MatchmakingDuration { get; }
    }
}