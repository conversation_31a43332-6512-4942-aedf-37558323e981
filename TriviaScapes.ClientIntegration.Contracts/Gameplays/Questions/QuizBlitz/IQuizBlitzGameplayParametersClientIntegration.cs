using System.Collections.Generic;
using TriviaScapes.ClientIntegration.Contracts.Inventory;
using Xm.Contracts.Abstractions;
using Xm.Gameplays.ClientIntegration.Contracts;

namespace TriviaScapes.ClientIntegration.Contracts.Gameplays.Questions.QuizBlitz
{
    [SerializableContract("gplayciqbgpp")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface IQuizBlitzGameplayParametersClientIntegration : IXmGameplayParametersClientIntegration
    {
        [SerializableContractProperty("qs", 0)]
        IQuestionSettingsClientIntegration Questions { get; }

        [SerializableContractProperty("ll", 1)]
        int LocalLives { get; }

        [SerializableContractProperty("d", 2)]
        int Duration { get; }

        [SerializableContractProperty("fad", 3)]
        int FastAnswerDuration { get; }

        [SerializableContractProperty("flad", 4)]
        int FastLettersAnswerDuration { get; }

        [SerializableContractProperty("am", 5)]
        AnswerMode AnswerMode { get; }

        [SerializableContractProperty("ah", 6)]
        IReadOnlyList<QuestionHintKind> AvailableHints { get; }

        [SerializableContractProperty("mt", 7)]
        IMotivationTextSettingsClientIntegration MotivationText { get; }

        [SerializableContractProperty("r", 8)]
        GlobalGameplayResourceKind Resource { get; }

        [SerializableContractProperty("far", 9)]
        int FastAnswerReward { get; }

        [SerializableContractProperty("rps", 10)]
        int RewardPerStar { get; }
    }
}