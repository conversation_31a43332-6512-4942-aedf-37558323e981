using System;

namespace TriviaScapes.ClientIntegration.Contracts.Gameplays
{
    public static class GlobalGameplayResourceKindExtensions
    {
        public static GameplayResourceKind ToGameplayResourceKind(this GlobalGameplayResourceKind kind)
        {
            switch (kind)
            {
                case GlobalGameplayResourceKind.Star:
                    return GameplayResourceKind.Star;

                case GlobalGameplayResourceKind.Coin:
                    return GameplayResourceKind.Coin;

                case GlobalGameplayResourceKind.Crystal:
                    return GameplayResourceKind.Crystal;
            }

            throw new InvalidOperationException($"Unknown resource: '{kind}'");
        }
    }
}