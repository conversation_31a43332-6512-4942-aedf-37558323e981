using TriviaScapes.ClientIntegration.Contracts.Campaign.Designs;
using Xm.Contracts.Abstractions;
using Xm.Gameplays.ClientIntegration.Contracts;
using Xm.UserRecords.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Campaign
{
    [XmUserRecordName("campst")]
    [SerializableContract("rcampst")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface ICampaignStateRecord
    {
        [SerializableContractProperty("dn", 0)]
        ICampaignLevelDesignNameClientIntegration DesignName { get; }

        [SerializableContractProperty("udn", 1)]
        ICampaignLevelDesignNameClientIntegration UnlockingDesignName { get; }

        [SerializableContractProperty("lvln", 2)]
        int LevelNumber { get; }

        [SerializableContractProperty("gplayprog", 3)]
        [SerializableContractAbstractProperty]
        IXmGameplayProgressClientIntegration GameplayProgress { get; }

        [SerializableContractProperty("a", 4)]
        int Attempt { get; }
    }
}