using TriviaScapes.ClientIntegration.Contracts.Misc.AgeRestrictions;
using TriviaScapes.ClientIntegration.Contracts.Misc.Urls;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Misc
{
    [SerializableContract("cimc")]
    public interface IMiscConfigurationClientIntegration
    {
        [SerializableContractProperty("u", 0)]
        IUrlSettingsClientIntegration Urls { get; }
        
        [SerializableContractProperty("as", 0)]
        IAgeRestrictionsClientIntegration AgeRestrictions { get; }
    }
}