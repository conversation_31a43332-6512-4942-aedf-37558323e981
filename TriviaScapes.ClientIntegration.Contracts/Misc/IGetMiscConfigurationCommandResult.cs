using Xm.AppSettings.Abstractions;
using Xm.Commands.Abstractions;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Misc
{
    [SerializableContract("cgmcr")]
    public interface IGetMiscConfigurationCommandResult : IXmCommandResult
    {
        [SerializableContractProperty("m", 0)]
        IXmAppSettings<IMiscConfigurationClientIntegration> Misc { get; }
    }
}