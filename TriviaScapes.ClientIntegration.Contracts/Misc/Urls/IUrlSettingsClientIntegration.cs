using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Misc.Urls
{
    [SerializableContract("cius")]
    public interface IUrlSettingsClientIntegration
    {
        [SerializableContractProperty("pp", 0)]
        string PrivacyPolicy { get; }
        
        [SerializableContractProperty("tac", 1)]
        string TermsAndConditions { get; }
        
        [SerializableContractProperty("qa", 2)]
        string QuizAuthors { get; }
        
        [SerializableContractProperty("su", 3)]
        string StoreUrl { get; }

        [SerializableContractProperty("ci", 4)]
        string CopyrightInfringement { get; }
    }
}