using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Misc.AgeRestrictions
{
    [SerializableContract("ciarci")]
    public interface IAgeRestrictionsClientIntegration
    {
        [SerializableContractProperty("mina", 0)]
        int MinAge { get; }
        
        [SerializableContractProperty("maxa", 1)]
        int MaxAge { get; }
        
        [SerializableContractProperty("ia", 2)]
        int InitialAge { get; }
    }
}