using System.Collections.Generic;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Onboarding
{
    [SerializableContract("cioq")]
    public interface IOnboardingQuestionClientIntegration
    {
        [SerializableContractProperty("n", 0)]
        string Name { get; }

        [SerializableContractProperty("mс", 1)]
        bool MultipleChoice { get; }

        [SerializableContractProperty("a", 2)]
        IReadOnlyList<IOnboardingAnswerClientIntegration> Answers { get; }
    }
}
