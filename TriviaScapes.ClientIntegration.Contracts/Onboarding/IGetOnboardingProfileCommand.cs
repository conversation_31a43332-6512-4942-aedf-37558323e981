using Xm.Commands.Abstractions;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Onboarding
{
    [SerializableContract("cgop")]
    public interface IGetOnboardingProfileCommand : IXmCommand
    {
        [SerializableContractProperty("pn", 0)]
        string ProfileName { get; }

        [SerializableContractProperty("cv", 1)]
        string CurrentVersion { get; }
    }
}
