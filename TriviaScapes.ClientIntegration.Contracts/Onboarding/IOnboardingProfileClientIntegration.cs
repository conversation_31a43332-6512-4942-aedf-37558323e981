using System.Collections.Generic;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Onboarding
{
    [SerializableContract("ciop")]
    public interface IOnboardingProfileClientIntegration
    {
        [SerializableContractProperty("wsn", 0)]
        string WelcomeScreenName { get; }

        [SerializableContractProperty("esn", 1)]
        string EndScreenName { get;}

        [SerializableContractProperty("esdim", 2)]
        int EndScreenDurationInMilliseconds { get; }

        [SerializableContractProperty("q", 3)]
        IReadOnlyList<IOnboardingQuestionClientIntegration> Questions { get; }

        [SerializableContractProperty("ssb", 4)]
        bool ShowSkipButton { get; }

        [SerializableContractProperty("essсd", 5)]
        int EndScreenStatusChangeDelay { get; }

        [SerializableContractProperty("pn", 6)]
        string ProfileName { get; }
    }
}
