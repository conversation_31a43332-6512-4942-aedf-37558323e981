using Xm.AppSettings.Abstractions;
using Xm.Commands.Abstractions;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Onboarding
{
    [SerializableContract("cgopr")]
    public interface IGetOnboardingProfileCommandResult : IXmCommandResult
    {
        [SerializableContractProperty("p", 0)]
        IXmAppSettings<IOnboardingProfileClientIntegration> Profile { get; }
    }
}
