using TriviaScapes.ClientIntegration.Contracts.Progress.Daily;
using TriviaScapes.ClientIntegration.Contracts.Progress.HardQuestion;
using TriviaScapes.ClientIntegration.Contracts.Progress.Progressions;
using Xm.Contracts.Abstractions;
using Xm.UserRecords.Abstractions;
using Xm.GameLives.ClientIntegration.Contracts;

namespace TriviaScapes.ClientIntegration.Contracts.Progress
{
    [XmUserRecordName("progress")]
    [SerializableContract("rprog")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface IProgressRecord
    {
        [SerializableContractProperty("cln", 0)]
        int CurrentLevelNumber { get; }

        [SerializableContractProperty("c", 1)]
        int Coins { get; }

        [SerializableContractProperty("s", 2)]
        int Stars { get; }

        [SerializableContractProperty("trqa", 3)]
        int TotalRightQuestionAnswers { get; }

        [SerializableContractProperty("twqa", 4)]
        int TotalWrongQuestionAnswers { get; }

        [SerializableContractProperty("lvs", 5)]
        IXmGameLiveStateClientIntegration Lives { get; }

        [SerializableContractProperty("dp", 7)]
        IDailyProgressClientIntegration DailyProgress { get; }

        [SerializableContractProperty("ras", 8)]
        IStreakContainerClientIntegration RightAnswerStreak { get; }

        [SerializableContractProperty("ios", 9)]
        bool IsOnboardingShown { get; }

        [SerializableContractProperty("hq", 10)]
        IHardQuestionProgressClientIntegration HardQuestions { get; }

        [SerializableContractProperty("cr", 11)]
        int Crystals { get; }

        [SerializableContractProperty("prgs", 12)]
        IProgressionsProgressClientIntegration Progressions { get; }
    }
}