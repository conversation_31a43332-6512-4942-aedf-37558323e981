using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Progress.Daily
{
    [SerializableContract("cidp")]
    public interface IDailyProgressClientIntegration
    {
        [SerializableContractProperty("d", 0)]
        int Day { get; }

        [SerializableContractProperty("s", 1)]
        IStreakContainerClientIntegration Streak { get; }

        [SerializableContractProperty("t", 2)]
        int Total { get; }

        [SerializableContractProperty("dst", 3)]
        long DayStartTime { get; }
    }
}