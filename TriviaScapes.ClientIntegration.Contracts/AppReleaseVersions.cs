namespace TriviaScapes.ClientIntegration.Contracts
{
    public static class AppReleaseVersions
    {
        public const int FinalizeRateDialog = 800; // 0.8

        public const int TelemetryInventoryItemDeprecation = 800; // 0.8

        public const int ExperimentSlots = 1000100; // 1.0.100
        public const int LetterQuestions = 1002000; // 1.2

        public const int RemoveAdPreloadingConditions = 1002100; // 1.2.100

        public const int OnboardingAnswerActionContextSection = 1006000; // 1.6.000
        public const int NewOnboardingIssue = 1006000; // 1.6.000
        public const int OnboardingBeforePresetInit = 1006000; // 1.6.000

        public const int NewGameplayStructure = 1007000; // 1.7.000

        public const int WordLetterQuestionsSeparateViewType = 1009000; // 1.9.000

        public const int PromoOffers_v2 = 2000000; // 2.0.000
    }
}