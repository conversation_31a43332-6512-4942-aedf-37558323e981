using TriviaScapes.ClientIntegration.Contracts.Tutorials;
using Xm.Commands;
using Xm.UserRecords;
using Xm.UserRecords.Commands;

namespace TriviaScapes.Progresses.Endpoint.CommandHandlers
{
    internal class UserTutorialHistoryRecordSynchronizeCommandHandler : XmUserRecordSynchronizeCommandHandler<IUserTutorialHistoryRecord, IUserTutorialHistoryRecordSynchronizeCommand, UserTutorialHistoryRecordSynchronizeCommandResultDto, ProgressesCommandContext>
    {
        public UserTutorialHistoryRecordSynchronizeCommandHandler(
            IXmCommandContainer commandContainer,
            ProgressesCommandContext context,
            IXmUserRecordManagerFactory recordManagerFactory
        )
                : base(commandContainer, context, recordManagerFactory)
        { }
    }
}