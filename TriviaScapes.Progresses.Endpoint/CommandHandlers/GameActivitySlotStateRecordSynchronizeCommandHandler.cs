using Xm.Commands;
using Xm.GameActivities.UserRecords.CommandHandlers;
using Xm.UserRecords;

namespace TriviaScapes.Progresses.Endpoint.CommandHandlers
{
    internal class GameActivitySlotStateRecordSynchronizeCommandHandler : XmGameActivitySlotStateRecordSynchronizeCommandHandler<ProgressesCommandContext>
    {
        public GameActivitySlotStateRecordSynchronizeCommandHandler(IXmCommandContainer commandContainer, ProgressesCommandContext context, IXmUserRecordManagerFactory recordManagerFactory)
            : base(commandContainer, context, recordManagerFactory)
        {
        }
    }
}