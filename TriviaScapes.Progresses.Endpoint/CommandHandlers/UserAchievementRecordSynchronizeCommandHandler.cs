using Xm.Commands;
using Xm.UserAchievements.Commands.CommandHandlers;
using Xm.UserRecords;

namespace TriviaScapes.Progresses.Endpoint.CommandHandlers
{
    internal class UserAchievementRecordSynchronizeCommandHandler : XmUserAchievementRecordSynchronizeCommandHandler<ProgressesCommandContext>
    {
        public UserAchievementRecordSynchronizeCommandHandler(IXmCommandContainer commandContainer, ProgressesCommandContext context, IXmUserRecordManagerFactory recordManagerFactory)
            : base(commandContainer, context, recordManagerFactory)
        { }
    }
}