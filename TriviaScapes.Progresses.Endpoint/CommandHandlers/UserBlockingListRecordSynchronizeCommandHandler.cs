using Xm.Commands;
using Xm.SocialInteraction.UserRecords.CommandHandlers;
using Xm.UserRecords;

namespace TriviaScapes.Progresses.Endpoint.CommandHandlers;

internal class UserBlockingListRecordSynchronizeCommandHandler : XmUserBlockingListRecordSynchronizeCommandHandler<ProgressesCommandContext>
{
    public UserBlockingListRecordSynchronizeCommandHandler(IXmCommandContainer commandContainer, ProgressesCommandContext context, IXmUserRecordManagerFactory recordManagerFactory)
        : base(commandContainer, context, recordManagerFactory)
    {
    }
}