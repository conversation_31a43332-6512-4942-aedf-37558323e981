using Xm.Commands;
using Xm.ReviewManager.UserRecords.CommandHandlers;
using Xm.UserRecords;

namespace TriviaScapes.Progresses.Endpoint.CommandHandlers
{
    internal class RateStateRecordSynchronizeCommandHandler : XmReviewManagerRateStateRecordSynchronizeCommandHandler<ProgressesCommandContext>
    {
        public RateStateRecordSynchronizeCommandHandler(
            IXmCommandContainer commandContainer,
            ProgressesCommandContext context,
            IXmUserRecordManagerFactory recordManagerFactory
        )
            : base(commandContainer, context, recordManagerFactory)
        {
        }
    }
}