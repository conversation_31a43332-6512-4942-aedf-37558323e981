using Xm.Commands;
using Xm.UserProfiles.UserRecords.Abstractions;
using Xm.UserRecords;
using Xm.UserRecords.Commands;

namespace TriviaScapes.Progresses.Endpoint.CommandHandlers;

internal class UserProfileSynchronizeCommandHandler : XmUserRecordSynchronize<PERSON>ommandHandler<IXmUserProfileRecord, IXmUserProfileRecordSynchronizeCommand, XmUserProfileRecordSynchronizeCommandResultDto, ProgressesCommandContext>
{
    public UserProfileSynchronizeCommandHandler(IXmCommandContainer commandContainer, ProgressesCommandContext context, IXmUserRecordManagerFactory recordManagerFactory) : base(commandContainer, context, recordManagerFactory)
    { }
}
