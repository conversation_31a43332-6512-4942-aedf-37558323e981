using TriviaScapes.ClientIntegration.Contracts.Settings;
using Xm.Commands;
using Xm.UserRecords;
using Xm.UserRecords.Commands;

namespace TriviaScapes.Progresses.Endpoint.CommandHandlers;

internal class AppSettingsRecordSynchronizeCommandHandler : XmUserRecordSynchronizeCommandHandler<IAppSettingsRecord, IAppSettingsRecordSynchronizeCommand, AppSettingsRecordSynchronizeCommandResultDto, ProgressesCommandContext>
{
    public AppSettingsRecordSynchronizeCommandHandler(IXmCommandContainer commandContainer, ProgressesCommandContext context, IXmUserRecordManagerFactory recordManagerFactory)
        : base(commandContainer, context, recordManagerFactory)
    {
    }
}