using Xm.Commands;
using Xm.UserInventory.UserRecords.CommandHandlers;
using Xm.UserRecords;

namespace TriviaScapes.Progresses.Endpoint.CommandHandlers
{
    internal class UserInventoryRecordSynchronizeCommandHandler : XmUserInventoryRecordSynchronizeCommandHandler<ProgressesCommandContext>
    {
        public UserInventoryRecordSynchronizeCommandHandler(IXmCommandContainer commandContainer, ProgressesCommandContext context, IXmUserRecordManagerFactory recordManagerFactory)
            : base(commandContainer, context, recordManagerFactory)
        {
        }
    }
}