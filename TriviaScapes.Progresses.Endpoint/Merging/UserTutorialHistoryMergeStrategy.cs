using TriviaScapes.ClientIntegration.Contracts.Tutorials;
using Xm.UserRecords.Merging;

namespace TriviaScapes.Progresses.Endpoint.Merging
{
	internal class UserTutorialHistoryMergeStrategy : XmUserRecordDeltaMergeStrategy<IUserTutorialHistoryRecord, UserTutorialHistoryRecordDto>
	{
		public UserTutorialHistoryMergeStrategy()
		{
			Delta.Merge(r => r.Items);
		}

		protected override UserTutorialHistoryRecordDto ToImplementation(IUserTutorialHistoryRecord record)
		{
			return new UserTutorialHistoryRecordDto(record);
		}
	}
}