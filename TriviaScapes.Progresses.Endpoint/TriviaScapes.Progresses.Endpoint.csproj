<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Xm.Competitions.UserRecords" Version="6.0.203.59" />
    <PackageReference Include="Xm.Contracts.Schemas.Integration" Version="6.1.241.291" />
    <PackageReference Include="Xm.EventBus.RoutingModule" Version="6.0.214.91" />
    <PackageReference Include="Xm.SocialInteraction.UserRecords" Version="6.0.202.830" />
    <PackageReference Include="Vx.ApiGateway.Integration" Version="6.0.265.479" />
    <PackageReference Include="Xm.Aerospike" Version="9.4.267.140" />
    <PackageReference Include="Xm.AspNetCore" Version="6.3.274.8" />
    <PackageReference Include="Xm.Commands.ApiGateway" Version="6.0.231.917" />
    <PackageReference Include="Xm.Contracts" Version="6.0.204.259" />
    <PackageReference Include="Xm.Dev" Version="1.0.185.564">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Xm.ReviewManager.UserRecords" Version="6.0.202.956" />
    <PackageReference Include="Xm.UserAchievements.Commands" Version="6.1.235.370" />
    <PackageReference Include="Xm.UserInventory.UserRecords" Version="6.0.211.193" />
    <PackageReference Include="Xm.UserProfiles.UserRecords" Version="6.0.207.517" />
    <PackageReference Include="Xm.UserRecords.Aerospike" Version="6.0.271.231" />
    <PackageReference Include="Xm.UserRecords.Commands" Version="6.0.271.231" />
    <PackageReference Include="Xm.Users.Integration.Events" Version="6.1.248.682" />
    <PackageReference Include="Xm.Users.Integration.Events.Contracts" Version="6.1.248.682" />
    <PackageReference Include="Xm.Users.Integration.Events.Dto" Version="6.1.248.682" />
    <PackageReference Include="Xm.GameActivities.UserRecords" Version="6.1.284.834" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TriviaScapes.Dto\TriviaScapes.Dto.csproj" />
  </ItemGroup>

</Project>
