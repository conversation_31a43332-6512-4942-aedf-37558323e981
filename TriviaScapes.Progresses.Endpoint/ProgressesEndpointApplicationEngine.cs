using Autofac;
using TriviaScapes.Dto;
using Xm.Aerospike.Engine;
using Xm.ApplicationEngine.Config;
using Xm.ApplicationEngine.Engines;
using Xm.Commands;
using Xm.Contracts.Schemas.Integration;
using Xm.EventBus.RoutingModule;
using Xm.Gdpr.Client;
using Xm.Gdpr.Integration;
using Xm.UserProfiles.Integration.Events;
using Xm.Users.Integration.Events;
using Xm.Utils;
using Xm.Utils.Runtime;

namespace TriviaScapes.Progresses.Endpoint
{
    public class ProgressesEndpointApplicationEngine : DefaultApplicationEngine
    {
        protected override void RegisterModules(ContainerBuilder modulesBuilder)
        {
            base.RegisterModules(modulesBuilder);

            RegisterModule(modulesBuilder, _ => new XmCommandModule<ProgressesCommandContext>(SerializableContractRepository.Instance));
            RegisterModule(modulesBuilder, _ => new AerospikeServiceModule());
            RegisterModule(modulesBuilder, _ => new AerospikeConfigurationModule(ApplicationRuntimeInfo.Current.ProjectGroup.FirstUpper()));
            RegisterModule(modulesBuilder, _ => new ProgressesEngineModule(SerializableContractRepository.Instance));
            RegisterModule(modulesBuilder, _ => new ProgressesApiGatewayIntegrationModule(SerializableContractRepository.Instance));
            RegisterModule<GdprIntegrationModule>(modulesBuilder);
            RegisterModule(modulesBuilder, _ => new GdprClientModule(ConfigLayers.Application));
            RegisterModule<SerializableContractSchemaIntegrationEngineModule>(modulesBuilder);

            RegisterModule<XmUserProfileEventAdapterModule>(modulesBuilder);
            RegisterModule<XmUserEventAdapterModule>(modulesBuilder);
            RegisterModule(modulesBuilder, _ => new XmEventBusRoutingModule());
        }
    }
}