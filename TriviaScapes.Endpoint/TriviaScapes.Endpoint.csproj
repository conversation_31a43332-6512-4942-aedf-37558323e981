<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Vx.ApiGateway.Integration" Version="6.0.265.479" />
    <PackageReference Include="Xm.Adjust.Integration" Version="6.0.260.883" />
    <PackageReference Include="Xm.Advertisement.GameActivities" Version="6.1.270.267" />
    <PackageReference Include="Xm.Advertisement.Mediations.Amazon" Version="6.1.270.267" />
    <PackageReference Include="Xm.GameGoals" Version="6.0.239.270" />
    <PackageReference Include="Xm.Billing.UserRewards" Version="6.1.286.840" />
    <PackageReference Include="Xm.Competitions.Presets" Version="6.0.203.59" />
    <PackageReference Include="Xm.Contracts.Schemas.Integration" Version="6.1.241.291" />
    <PackageReference Include="Xm.EventBus.RoutingModule" Version="6.0.214.91" />
    <PackageReference Include="Xm.ExperimentalFeatures.Commands" Version="6.1.256.347" />
    <PackageReference Include="Xm.ExperimentalFeatures.Presets" Version="6.1.256.347" />
    <PackageReference Include="Xm.GameActivities.Events" Version="6.1.284.834" />
    <PackageReference Include="Xm.Logging.EventBus" Version="6.2.225.102" />
    <PackageReference Include="Xm.PromoOffers.Commands" Version="6.1.271.611" />
    <PackageReference Include="Xm.PromoOffers.GameActivities" Version="6.1.271.611" />
    <PackageReference Include="Xm.PromoOffers.Presets" Version="6.1.271.611" />
    <PackageReference Include="Xm.UserAchievements" Version="6.1.235.370" />
    <PackageReference Include="Xm.UserAchievements.Commands" Version="6.1.235.370" />
    <PackageReference Include="Xm.UserRewards.Commands" Version="6.0.224.877" />
    <PackageReference Include="Xm.Advertisement.Billing.Integration" Version="6.1.270.267" />
    <PackageReference Include="Xm.Advertisement.Commands" Version="6.1.270.267" />
    <PackageReference Include="Xm.AspNetCore" Version="6.3.274.8" />
    <PackageReference Include="Xm.Billing.Commands" Version="6.1.286.840" />
    <PackageReference Include="Xm.Commands.ApiGateway" Version="6.0.231.917" />
    <PackageReference Include="Xm.Contracts" Version="6.0.204.259" />
    <PackageReference Include="Xm.Dev" Version="1.0.185.564">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Xm.Aerospike" Version="9.4.267.140" />
    <PackageReference Include="Xm.Commands" Version="6.0.231.917" />
    <PackageReference Include="Xm.ApplicationEngine" Version="6.3.274.8" />
    <PackageReference Include="Xm.GameLives" Version="6.0.208.899" />
    <PackageReference Include="Xm.LevelMaps" Version="6.1.249.541" />
    <PackageReference Include="Xm.Presets.Commands" Version="6.4.273.367" />
    <PackageReference Include="Xm.Presets.Aerospike" Version="6.4.273.367" />
    <PackageReference Include="Xm.QuestionDelivery.Annotations" Version="6.4.282.239" />
    <PackageReference Include="Xm.Contracts.SourceGeneration.Net" Version="6.0.204.259">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Xm.QuestionDelivery.LevelMaps" Version="6.4.282.239" />
    <PackageReference Include="Xm.ReviewManager.Presets" Version="6.0.202.956" />
    <PackageReference Include="Xm.Localizations.Presets" Version="6.1.250.828" />
    <PackageReference Include="Xm.Users.Integration.Contracts" Version="6.1.248.682" />
    <PackageReference Include="Xm.GameActivities.Commands" Version="6.1.284.834" />
    <PackageReference Include="Xm.GameActivities.Presets" Version="6.1.284.834" />
    <PackageReference Include="Xm.GameActivities.RazorPages" Version="6.1.284.834" />
    <PackageReference Include="Xm.GameActivities.Attempts" Version="6.1.284.834" />
    <PackageReference Include="Xm.Gameplays.Commands" Version="6.0.254.899" />
    <PackageReference Include="Xm.ConfigExtensions" Version="6.0.202.933" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TriviaScapes.Dto\TriviaScapes.Dto.csproj" />
    <ProjectReference Include="..\TriviaScapes.QuestionDelivery.Presets\TriviaScapes.QuestionDelivery.Presets.csproj" />
  </ItemGroup>

</Project>
