using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts.Rewards.Levels;

namespace TriviaScapes.Endpoint.Rewards.Levels;

public class LevelRewardSettings : ILevelRewardClientIntegration
{
    private const int DefaultAdditionalCoins = 100;

    public LevelRewardSettings()
    {
        AdditionalCoins = DefaultAdditionalCoins;
    }

    [DefaultValue(DefaultAdditionalCoins)]
    public int AdditionalCoins { get; internal set; }

    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    [Config(DisableAutoCreate = true)]
    public LevelRewardMultiplicationSettings MultiplicationSettings { get; internal set; }

    [DefaultValue(LevelRewardMultiplicationType.ArrowMultiply)]
    public LevelRewardMultiplicationType RewardMultiplicationType { get; internal set; }

    ILevelRewardMultiplicationSettingsClientIntegration ILevelRewardClientIntegration.MultiplicationSettings => MultiplicationSettings;
}