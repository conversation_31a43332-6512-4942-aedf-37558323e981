using TriviaScapes.Endpoint.Extensions;
using Xm.Devices.Integration.Contracts.Extensions;
using Xm.Users.Integration.Contracts.Extensions;

namespace TriviaScapes.Endpoint.Onboarding.Extensions;

internal static class OnboardingSessionExtensions
{
    public static OnboardingProfileSelectionRuleConditionContext ToOnboardingProfileSelectionRuleConditionContext(this IOnboardingSession session)
    {
        return new OnboardingProfileSelectionRuleConditionContext
        {
            Language = session.Language?.ToString(),
            AppVersion = session.ClientAppStart?.AppVersion,
            OperatingSystem = session.DeviceOs?.Kind.ToString(),
            OperatingSystemMajorVersion = session.DeviceOs?.MajorVersion,
            DeviceRawModel = session.DeviceModel?.Raw,
            DeviceFormFactor = session.DeviceForm?.Factor.ToString(),
            DeviceAppStoreVendor = session.DeviceStore?.Vendor.ToString(),
            DeviceModelName = session.DeviceModel?.Name,
            DeviceModelBrand = session.DeviceModel?.Brand,
            DeviceScreenWidth = session.DeviceScreen?.Width ?? 0,
            DeviceScreenHeight = session.DeviceScreen?.Height ?? 0,
            DeviceScreenAspectRatio = session.DeviceScreen?.GetAspectRatio() ?? 0,
            DeviceRAM = session.DeviceSpecifications?.SystemMemorySize ?? 0,
            GeoCountryName = session.GeoLocation?.CountryName,
            GeoCountryCode = session.GeoLocation?.CountryCode,
            GeoCountryTier = session.GeoLocation?.CountryTier ?? 0,
            DayFromInstall = session.UserIdentity.GetDaysSinceCreatedOn(),
            RemainderOfDividingPublicIdBy2 = session.UserIdentity.GetRemainderOfDividingPublicId(2),
            RemainderOfDividingPublicIdBy3 = session.UserIdentity.GetRemainderOfDividingPublicId(3),
            RemainderOfDividingPublicIdBy4 = session.UserIdentity.GetRemainderOfDividingPublicId(4),
            RemainderOfDividingPublicIdBy10 = session.UserIdentity.GetRemainderOfDividingPublicId(10)
        };
    }
}