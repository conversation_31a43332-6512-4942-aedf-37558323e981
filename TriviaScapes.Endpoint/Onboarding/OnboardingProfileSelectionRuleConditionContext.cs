namespace TriviaScapes.Endpoint.Onboarding;

public class OnboardingProfileSelectionRuleConditionContext
{
    public string Language { get; init; }

    public int? AppVersion { get; init; }

    public string OperatingSystem { get; init; }

    public string OperatingSystemMajorVersion { get; init; }

    public string DeviceRawModel { get; init; }

    public string DeviceFormFactor { get; init; }

    public string DeviceAppStoreVendor { get; init; }

    public string DeviceModelName { get; init; }

    public string DeviceModelBrand { get; init; }

    public int DeviceScreenWidth { get; init; }

    public int DeviceScreenHeight { get; init; }

    public double DeviceScreenAspectRatio { get; init; }

    public int DeviceRAM { get; init; }

    public string GeoCountryName { get; init; }

    public string GeoCountryCode { get; init; }

    public int GeoCountryTier { get; init; }

    public int DayFromInstall { get; init; }

    public int RemainderOfDividingPublicIdBy2 { get; init; }

    public int RemainderOfDividingPublicIdBy3 { get; init; }

    public int RemainderOfDividingPublicIdBy4 { get; init; }

    public int RemainderOfDividingPublicIdBy10 { get; init; }
}