using System;
using Lr.Basic.Config;
using Xm.ApplicationEngine.Config;

namespace TriviaScapes.Endpoint.Onboarding
{
    [AttributeUsage(AttributeTargets.Property, AllowMultiple = false, Inherited = true)]
    internal sealed class OnboardingProfileAttribute : LocalConfigVariantReferenceAttribute
    {
        protected override string LayerName => ConfigLayers.Gameplay;

        public override string GetVariantName(IConfigSerializerContext context)
        {
            return OnboardingProfileConfigVariantSource.VariantName;
        }
    }
}