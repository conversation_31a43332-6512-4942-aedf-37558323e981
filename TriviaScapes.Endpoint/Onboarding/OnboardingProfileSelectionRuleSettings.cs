using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using Lr.Basic.Expressions;

namespace TriviaScapes.Endpoint.Onboarding;

internal class OnboardingProfileSelectionRuleSettings
{
    [ConfigUnique]
    [ConfigCollectionDisplayKey]
    [ConfigCollectionKey]
    [ConfigDisplayStyle(IsHeader = true)]
    public string Name { get; private set; }

    [ConfigDisplayStyle(IsHeader = true)]
    public bool Enabled { get; private set; }

    [ConfigDisplayStyle(IsHeader = true)]
    public Expression<OnboardingProfileSelectionRuleConditionContext> Condition { get; private set; }

    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public OnboardingProfileSelectionRuleItemSettings[] Items { get; private set; }
}