using Xm.ClientApps.Integration.Contracts;
using Xm.Devices.Integration.Contracts;
using Xm.Geo.Integration.Contracts;
using Xm.Language;
using Xm.Users.Integration.Contracts;

namespace TriviaScapes.Endpoint.Onboarding;

public interface IOnboardingSession
{
    public XmLanguage? Language { get; }

    public IXmDeviceModelIntegration DeviceModel { get; }

    public IXmDeviceFormIntegration DeviceForm { get; }

    public IXmDeviceSpecificationsIntegration DeviceSpecifications { get; }

    public IXmDeviceScreenIntegration DeviceScreen { get; }

    public IXmDeviceOsIntegration DeviceOs { get; }

    public IXmDeviceStoreIntegration DeviceStore { get; }

    public IXmClientAppStartIntegration ClientAppStart { get; }

    public IXmGeoLocationIntegration GeoLocation { get; }

    public IXmUserIdentityIntegration UserIdentity { get; }
}