using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;

namespace TriviaScapes.Endpoint.Onboarding
{
    [DisplayName("Onboarding")]
    internal class OnboardingConfiguration
    {
        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public OnboardingProfileSettings[] Profiles { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public OnboardingProfileSelectionRuleSettings[] Rules { get; private set; }
    }
}