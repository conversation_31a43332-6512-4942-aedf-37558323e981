using System.Collections.Generic;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts.Onboarding;

namespace TriviaScapes.Endpoint.Onboarding
{
    internal class OnboardingQuestionSettings : IOnboardingQuestionClientIntegration
    {
        [ConfigUnique]
        [ConfigCollectionDisplayKey]
        [ConfigCollectionKey]
        [ConfigDisplayStyle(IsHeader = true)]
        public string Name { get; private set; }

        [ConfigDisplayStyle(IsHeader = true)]
        public bool MultipleChoice { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public OnboardingAnswerSettings[] Answers { get; private set; }

        IReadOnlyList<IOnboardingAnswerClientIntegration> IOnboardingQuestionClientIntegration.Answers => Answers;
    }
}