using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts.Onboarding;

namespace TriviaScapes.Endpoint.Onboarding
{
    internal class OnboardingAnswerSettings : IOnboardingAnswerClientIntegration
    {
        [ConfigUnique]
        [ConfigCollectionDisplayKey]
        [ConfigCollectionKey]
        [ConfigDisplayStyle(IsHeader = true)]
        public string Name { get; private set; }

        [ConfigDisplayStyle(IsHeader = true)]
        public string ImageUrl { get; private set; }
    }
}