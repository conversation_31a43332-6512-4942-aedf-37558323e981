using System;
using System.Collections.Generic;
using System.Linq;
using Lr.Basic.Expressions;
using TriviaScapes.ClientIntegration.Contracts;
using TriviaScapes.ClientIntegration.Contracts.Onboarding;
using TriviaScapes.Endpoint.Onboarding.Extensions;
using Xm.AppSettings.Abstractions;
using Xm.Caching;
using Xm.Contracts;
using Xm.Contracts.Abstractions;
using Xm.Utils;
using Xm.Utils.ChangeableObjects;

namespace TriviaScapes.Endpoint.Onboarding
{
    internal class OnboardingProfileConfigRepository : IOnboardingProfileRepository, IDisposable
    {
        private readonly IConvertedChangeableObject<IDictionary<string, IOnboardingProfileClientIntegration>> _profiles;
        private readonly IConvertedChangeableObject<IReadOnlyList<OnboardingProfileSelectionRuleSettings>> _rules;
        private readonly ISerializableContractRepository _serializableContractRepository;

        public OnboardingProfileConfigRepository(
            IChangeableObject<OnboardingConfiguration> configuration,
            ISerializableContractRepository serializableContractRepository
        )
        {
            _profiles = configuration.CreateConverted(c =>
                c.Profiles?.ToDictionary<OnboardingProfileSettings, string, IOnboardingProfileClientIntegration>(
                    p => p.Name,
                    p => p,
                    StringComparer.OrdinalIgnoreCase
                ) ?? new Dictionary<string, IOnboardingProfileClientIntegration>()
            );

            _rules = configuration.CreateConverted(c => c.Rules.Where(r => r.Enabled).ToArray());

            _serializableContractRepository = serializableContractRepository;
        }

        public void Dispose()
        {
            _profiles?.Dispose();
        }

        public IXmAppSettings<IOnboardingProfileClientIntegration> Get(string name, IOnboardingSession session)
        {
            var context = session.ToOnboardingProfileSelectionRuleConditionContext();
            var appVersion = session.ClientAppStart.AppVersion;

            if (!TryGetProfile(name, out var profile))
            {
                if (appVersion < AppReleaseVersions.NewOnboardingIssue)
                {
                    throw new InvalidOperationException("Profile is not found").SetData("Name", name);
                }

                var rule = _rules.Value.FirstOrDefault(r => r.Condition.IsMatch(context));

                var nameFromRule = rule?
                                   .Items
                                   .RandomOrDefault(x => x.Weight)?
                                   .Profile;

                if (!TryGetProfile(nameFromRule, out profile))
                {
                    throw new InvalidOperationException("Profile is not found from rule").SetData("Name", nameFromRule).SetData("Rule", rule?.Name);
                }
            }

            return new XmAppSettings<IOnboardingProfileClientIntegration>(appVersion, _serializableContractRepository.CalculateVersion(profile), profile);
        }

        private bool TryGetProfile(string name, out IOnboardingProfileClientIntegration profile)
        {
            if (!string.IsNullOrEmpty(name))
            {
                return _profiles.Value.TryGetValue(name, out profile);
            }

            profile = null;

            return false;
        }
    }
}