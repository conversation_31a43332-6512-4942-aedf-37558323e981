using Autofac;
using Xm.ApplicationEngine;
using Xm.ApplicationEngine.Config;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.Endpoint.Onboarding
{
    internal class OnboardingEngineModule : EngineModule
    {
        private readonly ISerializableContractRepository _serializableContractRepository;
        
        public OnboardingEngineModule(ISerializableContractRepository serializableContractRepository)
        {
            _serializableContractRepository = serializableContractRepository;
        }

        public override void RegisterConfigTypes(IConfigStorageBuilder configStorageBuilder)
        {
            base.RegisterConfigTypes(configStorageBuilder);

            configStorageBuilder.RegisterGameplayConfigType<OnboardingConfiguration>();
            configStorageBuilder.RegisterGameplayVariantSource<OnboardingProfileConfigVariantSource>();
        }

        protected override void RegisterComponents(ContainerBuilder builder)
        {
            base.RegisterComponents(builder);

            builder.RegisterType<OnboardingProfileConfigRepository>()
                   .WithParameter(TypedParameter.From(_serializableContractRepository))
                   .As<IOnboardingProfileRepository>()
                   .SingleInstance();
        }
    }
}