using System.Collections.Generic;
using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts.Onboarding;

namespace TriviaScapes.Endpoint.Onboarding
{
    internal class OnboardingProfileSettings : IOnboardingProfileClientIntegration
    {
        private const int DefaultEndScreenDuration = 6000;
        private const int DefaultEndScreenStatusChangeDelay = 2000;

        [ConfigUnique]
        [ConfigCollectionDisplayKey]
        [ConfigCollectionKey]
        [ConfigDisplayStyle(IsHeader = true)]
        public string Name { get; set; }

        [ConfigDisplayStyle(IsHeader = true)]
        public string WelcomeScreenName { get; private set; }

        [ConfigDisplayStyle(IsHeader = true)]
        public string EndScreenName { get; private set; }

        [DefaultValue(DefaultEndScreenDuration)]
        public int EndScreenDurationInMilliseconds { get; private set; }

        public bool ShowSkipButton { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public OnboardingQuestionSettings[] Questions { get; private set; }

        [DefaultValue(DefaultEndScreenStatusChangeDelay)]
        [Description("In milliseconds")]
        public int EndScreenStatusChangeDelay { get; private set; }

        IReadOnlyList<IOnboardingQuestionClientIntegration> IOnboardingProfileClientIntegration.Questions => Questions;
        string IOnboardingProfileClientIntegration.ProfileName => Name;
    }
}