using System.Collections;
using System.Collections.Generic;
using TriviaScapes.ClientIntegration.Contracts.GameActivities;
using Xm.GameActivities.Presets.Scenarios;

namespace TriviaScapes.Endpoint.GameActivities;

public class GameActivitySettings : IEnumerable<XmGameActivityScenarioReference>
{
    [GameActivityScenario(GameActivitySlot.Lobby1)]
    public string Lobby1Scenario { get; internal set; }

    [GameActivityScenario(GameActivitySlot.Lobby2)]
    public string Lobby2Scenario { get; internal set; }

    [GameActivityScenario(GameActivitySlot.Lobby3)]
    public string Lobby3Scenario { get; internal set; }

    [GameActivityScenario(GameActivitySlot.BrainScore)]
    public string BrainScoreScenario { get; internal set; }

    [GameActivityScenario(GameActivitySlot.ImageCollection)]
    public string ImageCollectionScenario { get; internal set; }

    public IEnumerator<XmGameActivityScenarioReference> GetEnumerator()
    {
        yield return GameActivitySlot.Lobby1.GetScenarioReference(Lobby1Scenario);
        yield return GameActivitySlot.Lobby2.GetScenarioReference(Lobby2Scenario);
        yield return GameActivitySlot.Lobby3.GetScenarioReference(Lobby3Scenario);
        yield return GameActivitySlot.BrainScore.GetScenarioReference(BrainScoreScenario);
        yield return GameActivitySlot.ImageCollection.GetScenarioReference(ImageCollectionScenario);
    }

    IEnumerator IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }
}