using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts.GameActivities.Transitions.Triggers.Levels;

namespace TriviaScapes.Endpoint.GameActivities.Transitions.Triggers;

[DisplayName("Levels.Finished Condition")]
[ConfigInherit]
public class LevelFinishedConditionGameActivityTransitionTriggerSettings : LevelConditionGameActivityTransitionTriggerSettings
{
    protected override GameActivityLevelTransitionTriggerBasis Basis => GameActivityLevelTransitionTriggerBasis.FinishedLevels;
}