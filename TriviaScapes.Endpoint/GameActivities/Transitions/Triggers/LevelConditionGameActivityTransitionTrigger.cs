using TriviaScapes.ClientIntegration.Contracts.GameActivities.Transitions.Triggers.Levels;
using Xm.ConfigExtensions.ClientIntegration.Contracts.Conditions;
using Xm.ConfigExtensions.Conditions;
using Xm.GameActivities;
using Xm.GameActivities.Transitions.Triggers;

namespace TriviaScapes.Endpoint.GameActivities.Transitions.Triggers;

public class LevelConditionGameActivityTransitionTrigger : XmGameActivityTransitionTrigger<ILevelConditionGameActivityTransitionTriggerParametersClientIntegration>
{
    private readonly GameActivityLevelTransitionTriggerBasis _basis;
    private readonly XmIntegerConditionContainer _condition;

    public LevelConditionGameActivityTransitionTrigger(GameActivityLevelTransitionTriggerBasis basis, XmIntegerConditionContainer condition)
    {
        _basis = basis;
        _condition = condition;
    }

    public override string GetDescription()
    {
        return $"{_condition.Condition} {_basis}";
    }

    protected override ILevelConditionGameActivityTransitionTriggerParametersClientIntegration GetParameters(XmGameActivityParameterContext context)
    {
        var value = _condition.Value;
        if (value == null)
        {
            return null;
        }

        return new LevelConditionGameActivityTransitionTriggerParametersClientIntegrationDto()
        {
            Basis = _basis,
            Condition = new XmIntegerConditionClientIntegrationDto(value)
        };
    }
}