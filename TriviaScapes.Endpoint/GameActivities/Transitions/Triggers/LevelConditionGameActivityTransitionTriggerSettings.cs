using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts.GameActivities.Transitions.Triggers.Levels;
using Xm.ConfigExtensions.Conditions;
using Xm.GameActivities.Transitions.Triggers;

namespace TriviaScapes.Endpoint.GameActivities.Transitions.Triggers;

[ConfigInherit]
public abstract class LevelConditionGameActivityTransitionTriggerSettings : XmGameActivityTransitionTriggerSettings
{
    private readonly XmIntegerConditionContainer _conditionContainer;

    protected LevelConditionGameActivityTransitionTriggerSettings()
    {
        _conditionContainer = new XmIntegerConditionContainer();
    }

    [Description(XmIntegerConditionContainer.ConfigDescription)]
    public string Condition
    {
        get => _conditionContainer.Condition;
        internal set => _conditionContainer.Condition = value;
    }

    protected abstract GameActivityLevelTransitionTriggerBasis Basis { get; }

    protected override IXmGameActivityTransitionTrigger GetTrigger()
    {
        return new LevelConditionGameActivityTransitionTrigger(Basis, _conditionContainer);
    }
}