using TriviaScapes.ClientIntegration.Contracts.GameActivities.Transitions.Triggers.Levels;
using Xm.GameActivities;
using Xm.GameActivities.Transitions.Triggers;

namespace TriviaScapes.Endpoint.GameActivities.Transitions.Triggers;

public class MinLevelGameActivityTransitionTrigger : XmGameActivityTransitionTrigger<IMinLevelGameActivityTransitionTriggerParametersClientIntegration>
{
    private readonly GameActivityLevelTransitionTriggerBasis _basis;
    private readonly int _level;

    public MinLevelGameActivityTransitionTrigger(GameActivityLevelTransitionTriggerBasis basis, int level)
    {
        _basis = basis;
        _level = level;
    }

    public override string GetDescription()
    {
        return $"At least {_level} {_basis}";
    }

    protected override IMinLevelGameActivityTransitionTriggerParametersClientIntegration GetParameters(XmGameActivityParameterContext context)
    {
        return new MinLevelGameActivityTransitionTriggerParametersClientIntegrationDto()
        {
            Basis = _basis,
            Level = _level
        };
    }
}