using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts.GameActivities.Transitions.Triggers.Levels;
using Xm.GameActivities.Transitions.Triggers;

namespace TriviaScapes.Endpoint.GameActivities.Transitions.Triggers;

[ConfigInherit]
public abstract class MinLevelGameActivityTransitionTriggerSettings : XmGameActivityTransitionTriggerSettings
{
    public int Level { get; internal set; }

    protected abstract GameActivityLevelTransitionTriggerBasis Basis { get; }

    protected override IXmGameActivityTransitionTrigger GetTrigger()
    {
        return new MinLevelGameActivityTransitionTrigger(Basis, Level);
    }
}