using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts.GameActivities.Transitions.Triggers.Levels;

namespace TriviaScapes.Endpoint.GameActivities.Transitions.Triggers;

[DisplayName("Levels.Started Condition")]
[ConfigInherit]
public class LevelStartedConditionGameActivityTransitionTriggerSettings : LevelConditionGameActivityTransitionTriggerSettings
{
    protected override GameActivityLevelTransitionTriggerBasis Basis => GameActivityLevelTransitionTriggerBasis.StartedLevels;
}
