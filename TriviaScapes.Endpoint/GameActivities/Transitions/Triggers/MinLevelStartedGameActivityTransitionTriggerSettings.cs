using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts.GameActivities.Transitions.Triggers.Levels;

namespace TriviaScapes.Endpoint.GameActivities.Transitions.Triggers;

[DisplayName("Levels.Min Started")]
[ConfigInherit]
public class MinLevelStartedGameActivityTransitionTriggerSettings : MinLevelGameActivityTransitionTriggerSettings
{
    protected override GameActivityLevelTransitionTriggerBasis Basis => GameActivityLevelTransitionTriggerBasis.StartedLevels;
}