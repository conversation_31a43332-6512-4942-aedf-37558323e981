using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts.GameActivities.TriviaRace;
using Xm.GameActivities;
using Xm.GameActivities.Events;

namespace TriviaScapes.Endpoint.GameActivities.TriviaRace;

[DisplayName("Trivia Race")]
[ConfigInherit]
public class TriviaRaceSettings : XmGameEventSettings
{
    [DefaultValue(100)]
    public int FinishQuestionsCount { get; internal set; }

    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public TriviaRaceRewardSetSettings Rewards { get; internal set; }

    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public TriviaRaceBotSettings Bots { get; internal set; }

    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public TriviaRaceUISettings UI { get; internal set; }

    protected override IXmGameActivity GetActivity()
    {
        return new TriviaRace(new TriviaRaceParametersClientIntegrationDto()
        {
            FinishQuestionsCount = FinishQuestionsCount,
            Rewards = new TriviaRaceRewardSetClientIntegrationDto(Rewards),
            Bots = new TriviaRaceBotParametersClientIntegrationDto(Bots),
            Ui = new TriviaRaceUIParametersClientIntegrationDto(UI),
        });
    }
}