using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts.GameActivities.TriviaRace;

namespace TriviaScapes.Endpoint.GameActivities.TriviaRace;

public class TriviaRaceBotGroupSettings : ITriviaRaceBotGroupClientIntegration
{
    [ConfigDisplayStyle(IsHeader = true)]
    [DefaultValue(1)]
    public int Count { get; internal set; }

    [ConfigDisplayStyle(IsHeader = true)]
    [DefaultValue(0)]
    public int MinProgress { get; internal set; }

    [ConfigDisplayStyle(IsHeader = true)]
    [DefaultValue(10)]
    public int MaxProgress { get; internal set; }
}