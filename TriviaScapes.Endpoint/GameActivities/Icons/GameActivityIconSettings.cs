using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts.GameActivities.Icons;

namespace TriviaScapes.Endpoint.GameActivities.Icons;

public class GameActivityIconSettings: IGameActivityIconSettingsClientIntegration
{
    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public GameActivitySlotIconSettings Lobby1 { get; internal set; }

    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public GameActivitySlotIconSettings Lobby2 { get; internal set; }

    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public GameActivitySlotIconSettings Lobby3 { get; internal set; }

    IGameActivitySlotIconSettingsClientIntegration IGameActivityIconSettingsClientIntegration.Lobby1 => Lobby1;
    IGameActivitySlotIconSettingsClientIntegration IGameActivityIconSettingsClientIntegration.Lobby2 => Lobby2;
    IGameActivitySlotIconSettingsClientIntegration IGameActivityIconSettingsClientIntegration.Lobby3 => Lobby3;
}