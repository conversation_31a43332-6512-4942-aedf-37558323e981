using Xm.Commands;
using Xm.GameActivities;
using Xm.GameActivities.Commands.CommandHandlers;

namespace TriviaScapes.Endpoint.GameActivities;

public class GetGameActivityScenarioCommandHandler : XmGetGameActivityScenarioCommandHandler<CommandContext>
{
    public GetGameActivityScenarioCommandHandler(IXmCommandContainer commandContainer, CommandContext context, IXmGameActivityIntegration gameActivities)
        : base(commandContainer, context, gameActivities)
    {
    }
}