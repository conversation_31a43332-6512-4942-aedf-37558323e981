using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts.GameActivities;
using Xm.GameActivities.Presets;
using Xm.GameActivities.Presets.Scenarios;

namespace TriviaScapes.Endpoint.GameActivities.PromoOffers;

[Disp<PERSON><PERSON><PERSON>("Promo Offers.Periodic 3 Scenario")]
[ConfigInherit]
public class GameActivityPeriodicOffers3ScenarioPresetModificator : XmGameActivityPresetModificator
{
    [GameActivityScenario(GameActivitySlot.PeriodicOffers3)]
    public string Sc<PERSON>rio { get; internal set; }

    protected override XmGameActivityScenarioReference GetReference()
    {
        return GameActivitySlot.PeriodicOffers3.GetScenarioReference(Scenario);
    }
}