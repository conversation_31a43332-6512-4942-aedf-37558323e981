using System.Collections;
using System.Collections.Generic;
using TriviaScapes.ClientIntegration.Contracts.GameActivities;
using Xm.GameActivities.Presets.Scenarios;

namespace TriviaScapes.Endpoint.GameActivities.PromoOffers;

public class PromoOfferActivitySettings : IEnumerable<XmGameActivityScenarioReference>
{
    [GameActivityScenario(GameActivitySlot.PeriodicOffers1)]
    public string Periodic1Scenario { get; internal set; }

    [GameActivityScenario(GameActivitySlot.PeriodicOffers2)]
    public string Periodic2Scenario { get; internal set; }

    [GameActivityScenario(GameActivitySlot.PeriodicOffers3)]
    public string Periodic3Scenario { get; internal set; }

    [GameActivityScenario(GameActivitySlot.PeriodicOffers4)]
    public string Periodic4Scenario { get; internal set; }

    [GameActivityScenario(GameActivitySlot.PeriodicOffers5)]
    public string Periodic5Scenario { get; internal set; }

    [GameActivityScenario(GameActivitySlot.PeriodicOffers6)]
    public string Periodic6Scenario { get; internal set; }

    [GameActivityScenario(GameActivitySlot.PeriodicOffers7)]
    public string Periodic7Scenario { get; internal set; }

    [GameActivityScenario(GameActivitySlot.PeriodicOffers8)]
    public string Periodic8Scenario { get; internal set; }

    [GameActivityScenario(GameActivitySlot.PeriodicOffers9)]
    public string Periodic9Scenario { get; internal set; }

    [GameActivityScenario(GameActivitySlot.PeriodicOffers10)]
    public string Periodic10Scenario { get; internal set; }

    public ShopOfferActivitySettings { get; internal set; }

    public IEnumerator<XmGameActivityScenarioReference> GetEnumerator()
    {
        yield return GameActivitySlot.PeriodicOffers1.GetScenarioReference(Periodic1Scenario);
        yield return GameActivitySlot.PeriodicOffers2.GetScenarioReference(Periodic2Scenario);
        yield return GameActivitySlot.PeriodicOffers3.GetScenarioReference(Periodic3Scenario);
        yield return GameActivitySlot.PeriodicOffers4.GetScenarioReference(Periodic4Scenario);
        yield return GameActivitySlot.PeriodicOffers5.GetScenarioReference(Periodic5Scenario);
        yield return GameActivitySlot.PeriodicOffers6.GetScenarioReference(Periodic6Scenario);
        yield return GameActivitySlot.PeriodicOffers7.GetScenarioReference(Periodic7Scenario);
        yield return GameActivitySlot.PeriodicOffers8.GetScenarioReference(Periodic8Scenario);
        yield return GameActivitySlot.PeriodicOffers9.GetScenarioReference(Periodic9Scenario);
        yield return GameActivitySlot.PeriodicOffers10.GetScenarioReference(Periodic10Scenario);
    }

    IEnumerator IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }
}