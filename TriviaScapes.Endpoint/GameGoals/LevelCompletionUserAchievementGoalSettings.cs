using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts.GameGoals;
using Xm.GameGoals.TargetValue;

namespace TriviaScapes.Endpoint.UserAchievements.Goal
{
    [ConfigInherit]
    [DisplayName("Level Completion")]
    public class LevelCompletionUserAchievementGoalSettings : XmTargetValueGameGoalSettings<ILevelCompletionGameGoalParametersClientIntegration>,
                                                              ILevelCompletionGameGoalParametersClientIntegration
    {
        protected override ILevelCompletionGameGoalParametersClientIntegration Parameters => this;

        protected override string GoalType => "Level Completion";
    }
}