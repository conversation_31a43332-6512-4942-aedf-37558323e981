using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts.GameGoals;
using Xm.GameGoals.TargetValue;

namespace TriviaScapes.Endpoint.GameGoals
{
    [ConfigInherit]
    [DisplayName("Minutes Played")]
    public class MinutesPlayedGameGoalSettings : XmTargetValueGameGoalSettings<IMinutesPlayedGameGoalParametersClientIntegration>, IMinutesPlayedGameGoalParametersClientIntegration
    {
        protected override IMinutesPlayedGameGoalParametersClientIntegration Parameters => this;

        protected override string GoalType => "Minutes Played";
    }
}