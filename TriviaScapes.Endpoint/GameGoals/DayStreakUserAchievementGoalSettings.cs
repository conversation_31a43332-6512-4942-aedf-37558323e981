using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts.GameGoals;
using Xm.GameGoals.TargetValue;

namespace TriviaScapes.Endpoint.UserAchievements.Goal
{
    [ConfigInherit]
    [DisplayName("Days In Row")]
    public class DayStreakUserAchievementGoalSettings : XmTargetValueGameGoalSettings<IDayStreakGameGoalParametersClientIntegration>,
                                                        IDayStreakGameGoalParametersClientIntegration
    {
        protected override IDayStreakGameGoalParametersClientIntegration Parameters => this;

        protected override string GoalType => "Days In Row";
    }
}