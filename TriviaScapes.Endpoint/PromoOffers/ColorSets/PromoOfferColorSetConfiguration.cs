using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.Endpoint.PromoOffers.InAppPurchases;

namespace TriviaScapes.Endpoint.PromoOffers.ColorSets;

[DisplayName("Color Sets")]
internal class PromoOfferColorSetConfiguration
{
    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    [DisplayName("In App Purchases")]
    public InAppPurchasePromoOfferColorSetSettings[] InAppPurchases { get; internal set; }
}