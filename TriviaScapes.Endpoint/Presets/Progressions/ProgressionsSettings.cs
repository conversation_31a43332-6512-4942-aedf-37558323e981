using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts.Presets.Progressions;

namespace TriviaScapes.Endpoint.Presets.Progressions
{
    public class ProgressionsSettings : IProgressionsSettingsClientIntegration
    {
        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public ProgressionSettings Letters { get; internal set; }

        IProgressionSettingsClientIntegration IProgressionsSettingsClientIntegration.Letters => Letters;
    }
}