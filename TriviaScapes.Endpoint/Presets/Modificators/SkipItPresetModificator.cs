using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using Xm.Presets.Modifications;

namespace TriviaScapes.Endpoint.Presets.Modificators
{
    [ConfigInherit]
    [DisplayName("SkipIt")]
    internal class SkipItPresetModificator : IXmPresetModificator<IPreset>
    {
        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public SkipItSettings SkipIt { get; private set; }

        public void Modify(IPreset preset)
        {
            preset.SkipIt = SkipIt;
        }
    }
}