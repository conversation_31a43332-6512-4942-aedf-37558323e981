using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.Endpoint.HapticFeedback;
using Xm.Presets.Modifications;

namespace TriviaScapes.Endpoint.Presets.Modificators;

[ConfigInherit]
[DisplayName("Haptic Feedback")]
internal class HapticFeedbackProfilePresetModificator : IXmPresetModificator<IPreset>
{
    [HapticFeedbackProfile]
    [ConfigDisplayStyle(IsHeader = true)]
    public string Profile { get; private set; }

    public void Modify(IPreset preset)
    {
        if (!string.IsNullOrEmpty(Profile))
        {
            preset.HapticFeedbackProfile = Profile;
        }
    }
}