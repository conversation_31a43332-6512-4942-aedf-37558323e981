namespace TriviaScapes.Endpoint.Presets
{
    public sealed class PresetCondition : IPresetExtrasCondition
    {
        internal PresetCondition()
        {
        }

        public int? AppVersion { get; init; }

        public string Language { get; init; }

        public string GeoCountryCode { get; init; }

        public int? GeoCountryTier { get; init; }

        public string OperatingSystem { get; init; }

        public string DeviceId { get; init; }

        public string Ip { get; init; }

        public bool UserIsDebug { get; init; }

        public int? UserAge { get; init; }

        public string CurrentSessionDeepLinks { get; set; }

        public string CurrentSessionInstallReferrer { get; set; }

        public string CurrentSessionSource2 { get; set; }

        public string CurrentSessionCampaign3 { get; set; }

        public string CurrentSessionAdSet4 { get; set; }

        public string CurrentSessionAd5 { get; set; }
    }
}