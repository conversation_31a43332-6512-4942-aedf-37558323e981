using TriviaScapes.ClientIntegration.Contracts.BrainScore;
using TriviaScapes.ClientIntegration.Contracts.GameActivities.Icons;
using TriviaScapes.ClientIntegration.Contracts.Presets.Gameplay;
using TriviaScapes.ClientIntegration.Contracts.Presets.GameResources;
using TriviaScapes.ClientIntegration.Contracts.Presets.LevelRewardDesign;
using TriviaScapes.ClientIntegration.Contracts.Presets.MainMenu;
using TriviaScapes.ClientIntegration.Contracts.Presets.SkipIt;
using TriviaScapes.ClientIntegration.Contracts.Presets.Tutorials;
using TriviaScapes.ClientIntegration.Contracts.Presets.UI;
using TriviaScapes.ClientIntegration.Contracts.Presets.UserBonuses;
using TriviaScapes.QuestionDelivery.Presets;
using Xm.Advertisement.Presets;
using Xm.Competitions.Presets;
using Xm.Contracts.Abstractions;
using Xm.ExperimentalFeatures.Presets;
using Xm.GameActivities.Presets;
using Xm.Localizations.Presets;
using Xm.PromoOffers.Presets;
using Xm.ReviewManager.Presets;
using Xm.UserAchievements.Presets;

namespace TriviaScapes.Endpoint.Presets
{
    [SerializableContract("p")]
    public interface IPreset :
        IQuestionDeliveryPreset,
        IXmReviewManagerPreset,
        IXmAdvertisementPreset,
        IXmLocalizationPreset,
        IXmCompetitionPreset,
        IXmUserAchievementPreset,
        IXmExperimentalFeaturePreset,
        IXmGameActivityPreset,
        IXmPromoOfferPreset,
        ILegacyv1Preset
    {
        [SerializableContractProperty("gr")]
        IGameResourcesPresetClientIntegration GameResources { get; set; }

        [SerializableContractProperty("adp")]
        string AdDisplayProfile { get; set; }

        [SerializableContractProperty("s")]
        string Store { get; set; }

        [SerializableContractProperty("ub")]
        IUserBonusesPresetClientIntegration UserBonuses { get; set; }

        [SerializableContractProperty("ldp")]
        string CampaignLevelDesignProfile { get; set; }

        [SerializableContractProperty("g")]
        IGameplayPresetClientIntegration Gameplay { get; set; }

        [SerializableContractProperty("t")]
        IUserTutorialsPresetClientIntegration Tutorials { get; set; }

        [SerializableContractProperty("lp")]
        string LeaderboardProfile { get; set; }

        [SerializableContractProperty("ap")]
        string AchievementProfile { get; set; }

        [SerializableContractProperty("mm")]
        IMainMenuPresetClientIntegration MainMenu { get; set; }

        [SerializableContractProperty("ui")]
        IUIPresetClientIntegration UI { get; set; }

        [SerializableContractProperty("icp")]
        string ImageCollectionProfile { get; set; }

        [SerializableContractProperty("gcadp")]
        string ImageCollectionAdDisplayProfile { get; set; }

        [SerializableContractProperty("lrd")]
        ILevelRewardDesignPresetClientIntegration LevelRewardDesign { get; set; }

        [SerializableContractProperty("skipit")]
        ISkipItSettingsClientIntegration SkipIt { get; set; }

        [SerializableContractProperty("bladp")]
        string BonusLevelAdDisplayProfile { get; set; }

        [SerializableContractProperty("op")]
        string OnboardingProfile { get; set; }

        [SerializableContractProperty("brains")]
        IBrainScoreSettingsClientIntegration BrainScore { get; set; }

        [SerializableContractProperty("hfp")]
        string HapticFeedbackProfile { get; set; }

        [SerializableContractProperty("campp")]
        string CampaignProfile { get; set; }

        [SerializableContractProperty("gactadp")]
        string GameActivityAdDisplayProfile { get; set; }

        [SerializableContractProperty("gactics")]
        IGameActivityIconSettingsClientIntegration GameActivityIconSettings { get; set; }
    }
}