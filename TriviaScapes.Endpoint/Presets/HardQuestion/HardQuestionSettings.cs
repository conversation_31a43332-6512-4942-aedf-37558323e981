using System.Collections.Generic;
using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts.Presets.HardQuestion;

namespace TriviaScapes.Endpoint.Presets.HardQuestion
{
    public class HardQuestionSettings : IHardQuestionSettingsClientIntegration
    {
        [DefaultValue(2)]
        public int AvailableFromLevel { get; internal set; }

        [DefaultValue(1)]
        [Description("Obsolete. Moved to the gameplay hard question settings")]
        public int KeysPerChapter { get; internal set; }

        [DefaultValue(3)]
        public int TotalKeysCount { get; internal set; }

        [DefaultValue(true)]
        public bool VisualizeHardQuestion { get; internal set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public HardQuestionRewardSettings[] Rewards { get; internal set; }

        IReadOnlyList<IHardQuestionRewardSettingsClientIntegration> IHardQuestionSettingsClientIntegration.Rewards => Rewards;
    }
}