using System;
using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts;
using Xm.Presets.Modifications;

namespace TriviaScapes.Endpoint.Presets.Legacy_v1;

[ConfigInherit]
[DisplayName("Legacy.v1.Promo Offers.View Design Experiment")]
[Obsolete("Since " + nameof(AppReleaseVersions.PromoOffers_v2))]
public class Legacyv1PromoOfferViewDesignExperimentModificator : IXmPresetModificator<IPreset>
{
    [ConfigDisplayStyle(IsHeader = true)]
    public string Name { get; private set; }

    public void Modify(IPreset preset)
    {
        if (!string.IsNullOrEmpty(Name))
        {
            preset.Legacyv1PromoOfferViewDesignExperiment = Name;
        }
    }
}