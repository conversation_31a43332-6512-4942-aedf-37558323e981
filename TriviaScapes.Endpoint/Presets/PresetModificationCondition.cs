using System.Collections.Generic;
using Xm.Presets.Modifications;

namespace TriviaScapes.Endpoint.Presets
{
    public sealed class PresetModificationCondition : IXmPresetModificationCondition, IPresetExtrasCondition
    {
        internal PresetModificationCondition()
        {
        }

        public bool AppUpdated { get; init; }

        public bool UserIsNew { get; init; }

        public string Language { get; init; }

        public int? AppVersion { get; init; }

        public string OperatingSystem { get; init; }

        public string GeoCountryCode { get; init; }

        public bool UserIsDebug { get; init; }

        public int DayFromInstall { get; init; }

        public string DeviceRawModel { get; init; }

        public string DeviceFormFactor { get; init; }

        public string DeviceAppStoreVendor { get; init; }

        public string OperatingSystemMajorVersion { get; init; }

        public string DeviceModelName { get; init; }

        public string DeviceModelBrand { get; init; }

        public int DeviceRAM { get; init; }

        public string GeoCountryName { get; init; }

        public int GeoCountryTier { get; init; }

        public int DeviceScreenWidth { get; init; }

        public int DeviceScreenHeight { get; init; }

        public double DeviceScreenAspectRatio { get; init; }

        public string PresetPath { get; set; }

        public IReadOnlyList<string> UserSegments { get; set; }

        public bool NewPresetSelected { get; set; }

        public int? UserAge { get; init; }

        public string DeviceId { get; init; }

        public IReadOnlyList<string> Tags { get; init; }

        public int UserCreatedOnUnixTimestamp { get; init; }

        public int RemainderOfDividingPublicIdBy2 { get; init; }

        public int RemainderOfDividingPublicIdBy3 { get; init; }

        public int RemainderOfDividingPublicIdBy4 { get; init; }

        public int RemainderOfDividingPublicIdBy10 { get; init; }

        public string CurrentSessionDeepLinks { get; set; }

        public string CurrentSessionInstallReferrer { get; set; }

        public string CurrentSessionSource2 { get; set; }

        public string CurrentSessionCampaign3 { get; set; }

        public string CurrentSessionAdSet4 { get; set; }

        public string CurrentSessionAd5 { get; set; }
    }
}