using System;
using System.ComponentModel;
using System.Linq;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;

namespace TriviaScapes.Endpoint.Presets.Tags;

[ConfigInherit]
[DisplayName("Device Raw Model")]
public class PresetDeviceModelTagSettings : PresetTagSettings
{
    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public string[] DeviceModels { get; private set; }

    public override bool IsApplicable(IPresetTagContext tagContext)
    {
        return tagContext.DeviceModel != null && DeviceModels.Contains(tagContext.DeviceModel.Raw, StringComparer.OrdinalIgnoreCase);
    }
}