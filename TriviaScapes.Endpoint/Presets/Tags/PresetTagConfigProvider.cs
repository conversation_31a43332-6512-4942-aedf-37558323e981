using System;
using System.Collections.Generic;
using System.Linq;
using Xm.Caching;
using Xm.Utils.ChangeableObjects;

namespace TriviaScapes.Endpoint.Presets.Tags;

internal class PresetTagConfigProvider : IPresetTagProvider, IDisposable
{
    private readonly IConvertedChangeableObject<PresetTagSettings[]> _tags;

    public PresetTagConfigProvider(IChangeableObject<PresetTagConfiguration> configuration)
    {
        _tags = configuration.CreateConverted(c => c.List.Where(x => x.IsActive).ToArray() ?? Array.Empty<PresetTagSettings>());
    }

    public void Dispose()
    {
        _tags?.Dispose();
    }

    public IReadOnlyList<string> Get(IPresetTagContext tagContext)
    {
        return _tags.Value
                    .Where(x => x.IsApplicable(tagContext))
                    .Select(x => x.Tag)
                    .ToArray();
    }
}