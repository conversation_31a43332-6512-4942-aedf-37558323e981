using System;
using System.ComponentModel;
using System.Linq;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;

namespace TriviaScapes.Endpoint.Presets.Tags;

[ConfigInherit]
[DisplayName("Device Id")]
public class PresetDeviceIdTagSettings : PresetTagSettings
{
    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public string[] DeviceIds { get; private set; }

    public override bool IsApplicable(IPresetTagContext tagContext)
    {
        return DeviceIds.Contains(tagContext.DeviceIdentity.UniqueId, StringComparer.OrdinalIgnoreCase);
    }
}