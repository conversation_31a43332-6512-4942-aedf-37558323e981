using Lr.Basic.Config.Attributes;

namespace TriviaScapes.Endpoint.Presets.Tags;

public abstract class PresetTagSettings
{
    [ConfigCollectionDisplayKey]
    [ConfigCollectionKey]
    [ConfigUnique]
    [ConfigDisplayStyle(IsHeader = true)]
    public string Tag { get; protected set; }

    [ConfigDisplayStyle(IsHeader = true)]
    public bool IsActive { get; protected set; }

    public abstract bool IsApplicable(IPresetTagContext tagContext);
}