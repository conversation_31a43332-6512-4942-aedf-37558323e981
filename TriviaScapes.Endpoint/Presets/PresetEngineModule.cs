using Autofac;
using TriviaScapes.Endpoint.Presets.Tags;
using Xm.ApplicationEngine.Config;
using Xm.Contracts.Abstractions;
using Xm.Presets.Registration;

namespace TriviaScapes.Endpoint.Presets
{
    internal class PresetEngineModule : XmPresetEngineModule<IPreset, PresetCondition, PresetModificationCondition>
    {
        public PresetEngineModule(ISerializableContractRepository serializableContractRepository)
            : base(serializableContractRepository, b => BuildComponents(b, serializableContractRepository))
        {
        }

        public override void RegisterConfigTypes(IConfigStorageBuilder configStorageBuilder)
        {
            base.RegisterConfigTypes(configStorageBuilder);

            configStorageBuilder.RegisterConfigType<PresetTagConfiguration>(ConfigLayer);
        }

        private static void BuildComponents(IXmPresetComponentBuilder<IPreset> builder, ISerializableContractRepository serializableContractRepository)
        {
            builder.UseAerospikeStorage(serializableContractRepository)
                   .UseAerospikeUserSegmentLoader()
                   .UseExperimentSlots(defaultSlot: ExperimentSlots.NewUsers1);
        }

        protected override void RegisterComponents(ContainerBuilder builder)
        {
            base.RegisterComponents(builder);

            builder.RegisterType<PresetTagConfigProvider>()
                   .As<IPresetTagProvider>()
                   .SingleInstance();
        }
    }
}