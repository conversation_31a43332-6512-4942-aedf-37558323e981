using System.Collections.Generic;
using System.Linq;
using TriviaScapes.ClientIntegration.Contracts.Presets;
using Xm.Adjust.Integration;
using Xm.Adjust.Integration.Contracts;
using Xm.Devices.Integration.Contracts;

namespace TriviaScapes.Endpoint.Presets
{
    public static class PresetExtrasConditionExtensions
    {
        public static T WithExtras<T>(this T condition, IReadOnlyDictionary<string, string> extras, IXmAdjustClientAttributionParser parser, IXmDeviceOsIntegration deviceOs)
            where T : IPresetExtrasCondition
        {
            if (extras != null)
            {
                condition.CurrentSessionDeepLinks = extras.GetValueOrDefault(PresetExtrasNamingConventions.DeepLinks);
                condition.CurrentSessionInstallReferrer = extras.GetValueOrDefault(PresetExtrasNamingConventions.InstallReferrer);

                condition.WithAdjust(extras, parser, deviceOs);
            }

            return condition;
        }

        private static void WithAdjust<T>(this T condition, IReadOnlyDictionary<string, string> extras, IXmAdjustClientAttributionParser parser, IXmDeviceOsIntegration deviceOs)
            where T : IPresetExtrasCondition
        {
            if (!extras.HasAdjust())
            {
                return;
            }

            var parsedAttribution = parser.Parse(
                new XmAdjustClientAttributionDto
                {
                    Network = extras.GetValueOrDefault(PresetExtrasNamingConventions.AdjustNetwork),
                    Campaign = extras.GetValueOrDefault(PresetExtrasNamingConventions.AdjustCampaign),
                    AdGroup = extras.GetValueOrDefault(PresetExtrasNamingConventions.AdjustAdGroup),
                    Creative = extras.GetValueOrDefault(PresetExtrasNamingConventions.AdjustCreative),
                    FbInstallReferrer = extras.GetValueOrDefault(PresetExtrasNamingConventions.AdjustFbInstallReferrer)
                },
                deviceOs
            );

            if (parsedAttribution != null)
            {
                condition.CurrentSessionSource2 = parsedAttribution.Tref2;
                condition.CurrentSessionCampaign3 = parsedAttribution.Tref3;
                condition.CurrentSessionAdSet4 = parsedAttribution.Tref4;
                condition.CurrentSessionAd5 = parsedAttribution.Tref5;
            }
        }

        private static bool HasAdjust(this IReadOnlyDictionary<string, string> extras)
        {
            var adjustKeys = new[]
            {
                PresetExtrasNamingConventions.AdjustNetwork,
                PresetExtrasNamingConventions.AdjustCampaign,
                PresetExtrasNamingConventions.AdjustAdGroup,
                PresetExtrasNamingConventions.AdjustCreative,
                PresetExtrasNamingConventions.AdjustFbInstallReferrer
            };

            return adjustKeys.Any(extras.ContainsKey);
        }
    }
}