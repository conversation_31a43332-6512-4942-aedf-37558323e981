using System;
using System.ComponentModel;
using System.Linq;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts;
using TriviaScapes.ClientIntegration.Contracts.BrainScore;
using TriviaScapes.ClientIntegration.Contracts.GameActivities.Icons;
using TriviaScapes.ClientIntegration.Contracts.Presets.Gameplay;
using TriviaScapes.ClientIntegration.Contracts.Presets.GameResources;
using TriviaScapes.ClientIntegration.Contracts.Presets.LevelRewardDesign;
using TriviaScapes.ClientIntegration.Contracts.Presets.MainMenu;
using TriviaScapes.ClientIntegration.Contracts.Presets.SkipIt;
using TriviaScapes.ClientIntegration.Contracts.Presets.Tutorials;
using TriviaScapes.ClientIntegration.Contracts.Presets.UI;
using TriviaScapes.ClientIntegration.Contracts.Presets.UserBonuses;
using TriviaScapes.ClientIntegration.Contracts.Presets.UserRewards;
using TriviaScapes.Endpoint.Achievements;
using TriviaScapes.Endpoint.Ads.DisplayProfiles;
using TriviaScapes.Endpoint.Billing.Stores;
using TriviaScapes.Endpoint.BrainScore;
using TriviaScapes.Endpoint.Campaign;
using TriviaScapes.Endpoint.Campaign.Designs;
using TriviaScapes.Endpoint.GameActivities;
using TriviaScapes.Endpoint.GameActivities.PromoOffers;
using TriviaScapes.Endpoint.GameActivities.Icons;
using TriviaScapes.Endpoint.HapticFeedback;
using TriviaScapes.Endpoint.ImageCollections.Annotations;
using TriviaScapes.Endpoint.Leaderboards;
using TriviaScapes.Endpoint.Onboarding;
using TriviaScapes.QuestionDelivery.Presets;
using Xm.Advertisement.Presets;
using Xm.Advertisement.Presets.Config;
using Xm.Competitions.Config.Annotations;
using Xm.Competitions.Presets;
using Xm.ExperimentalFeatures.ClientIntegration.Contracts.References;
using Xm.ExperimentalFeatures.Presets;
using Xm.GameActivities.ClientIntegration.Contracts.Scenarios;
using Xm.GameActivities.Presets;
using Xm.GameActivities.Presets.Scenarios;
using Xm.Localizations.Config.Annotations.Testing;
using Xm.Localizations.Presets;
using Xm.PromoOffers.Config.Annotations;
using Xm.PromoOffers.Presets;
using Xm.QuestionDelivery.Annotations;
using Xm.ReviewManager.Presets;
using Xm.UserAchievements.Annotations;
using Xm.UserAchievements.Presets;

namespace TriviaScapes.Endpoint.Presets
{
    [ConfigInherit]
    [DisplayName("Default")]
    internal class PresetSettings : IPreset
    {
        private const string MonetizationTabName = "Monetization";
        private const string Deprecatedv1TabName = "Deprecated v1";
        private const string GameActivitiesTabName = "GameActivities";

        [AdDisplayProfile]
        [ConfigDisplayStyle(TabName = MonetizationTabName)]
        public string AdDisplayProfile { get; internal set; }

        [Store]
        public string Store { get; internal set; }

        [XmReviewManagerRateDialogProfile]
        public string RateDialogProfile { get; internal set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public GameResourcesPresetSettings GameResources { get; internal set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public UserBonusesPresetSettings UserBonuses { get; internal set; }

        [XmQuestionIssuePresetGroup]
        public string QuestionIssuePresetGroup { get; internal set; }

        [Obsolete("Since " + nameof(AppReleaseVersions.NewGameplayStructure))]
        [Description("Deprecated - replaced with CampaignProfile")]
        [ConfigDisplayStyle(TabName = Deprecatedv1TabName)]
        public string LevelMapProfile { get; internal set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline, TabName = Deprecatedv1TabName)]
        [Obsolete("Since " + nameof(AppReleaseVersions.NewGameplayStructure))]
        [Description("Deprecated - moved to gameplay settings")]
        public UserRewardPresetSettings UserRewards { get; internal set; }

        [CampaignLevelDesignProfile]
        [DisplayName("Campaign Level Design Profile")]
        public string LevelDesignProfile { get; internal set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public GameplayPresetSettings Gameplay { get; internal set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public UserTutorialsPresetSettings Tutorials { get; internal set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public MainMenuPresetSettings MainMenu { get; internal set; }

        [XmUserAchievementBalanceProfile]
        public string AchievementBalanceProfile { get; internal set; }

        [XmAdvertisementPresetGroup]
        [ConfigDisplayStyle(TabName = MonetizationTabName)]
        public string AdPresetGroup { get; internal set; }

        [XmLocalizationTest]
        public string LocalizationTest { get; internal set; }

        [LeaderboardProfile]
        public string LeaderboardProfile { get; internal set; }

        [AchievementProfile]
        public string AchievementProfile { get; internal set; }

        [XmCompetitionRewardProfile]
        public string CompetitionRewardProfile { get; internal set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public UIPresetSettings UI { get; internal set; }

        [ImageCollectionProfile]
        public string ImageCollectionProfile { get; internal set; }

        [DisplayName("Game Challenge Sequence")]
        [Obsolete("Since " + nameof(AppReleaseVersions.NewGameplayStructure))]
        [Description("Deprecated - replaced with ImageCollection game activity slot")]
        [ConfigDisplayStyle(TabName = Deprecatedv1TabName)]
        public string GameChallengeProfile { get; internal set; }

        [ConfigDisplayStyle(TabName = Deprecatedv1TabName)]
        [Description("Deprecated - replaced with offer game activity slots")]
        [Obsolete("Since " + nameof(AppReleaseVersions.PromoOffers_v2))]
        public string PromoOfferProfile { get; internal set; }

        [ConfigDisplayStyle(TabName = Deprecatedv1TabName)]
        [Description("Deprecated - replaced with other promo offer view design experiment")]
        [Obsolete("Since " + nameof(AppReleaseVersions.PromoOffers_v2))]
        public string PromoOfferViewDesignExperiment { get; internal set; }

        [ConfigDisplayStyle(TabName = MonetizationTabName)]
        [XmPromoOfferViewDesignExperiment]
        [DisplayName("Promo Offer View Design Experiment")]
        public string PromoOfferViewDesignExperiment_v2 { get; internal set; }

        [AdDisplayProfile]
        [ConfigDisplayStyle(TabName = MonetizationTabName)]
        [DisplayName("Image Collection Ad Display Profile")]
        public string GameChallengeAdDisplayProfile { get; internal set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public LevelRewardDesignPresetSettings LevelRewardDesign { get; internal set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public SkipItSettings SkipIt { get; internal set; }

        [AdDisplayProfile]
        [ConfigDisplayStyle(TabName = MonetizationTabName)]
        public string BonusLevelAdDisplayProfile { get; internal set; }

        [Obsolete("Since " + nameof(AppReleaseVersions.OnboardingBeforePresetInit))]
        [DisplayName("Onboarding Profile (OBSOLETE)")]
        [OnboardingProfile]
        [ConfigDisplayStyle(TabName = Deprecatedv1TabName)]
        public string OnboardingProfile { get; internal set; }

        [DisplayName("Bonus Level Game Challenge Sequence")]
        [Description("Deprecated - replaced with bonus level type in campaign")]
        [Obsolete("Since " + nameof(AppReleaseVersions.NewGameplayStructure))]
        [ConfigDisplayStyle(TabName = Deprecatedv1TabName)]
        public string BonusLevelGameChallengeSequence { get; internal set; }

        [LevelQuestionProfile]
        public string TriviaMatrixQuestionProfile { get; internal set; }

        [ConfigDisplayStyle(TabName = GameActivitiesTabName, DisplayStyle = DisplayStyle.Inline)]
        public GameActivitySettings GameActivities { get; internal set; }

        [ConfigDisplayStyle(TabName = GameActivitiesTabName, DisplayStyle = DisplayStyle.Inline)]
        public GameActivityIconSettings GameActivitiesIconSettings { get; internal set; }

        [ConfigDisplayStyle(TabName = MonetizationTabName, DisplayStyle = DisplayStyle.Inline)]
        public PromoOfferActivitySettings Offers { get; internal set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public BrainScoreSettings BrainScore { get; internal set; }

        [HapticFeedbackProfile]
        public string HapticFeedbackProfile { get; internal set; }

        [CampaignProfile]
        public string CampaignProfile { get; internal set; }

        [AdDisplayProfile]
        [ConfigDisplayStyle(TabName = MonetizationTabName)]
        public string GameActivityAdDisplayProfile { get; internal set; }

        string IXmLocalizationPreset.LocalizationTest
        {
            get => LocalizationTest;
            set => throw new NotSupportedException();
        }

        string IPreset.Store
        {
            get => Store;
            set => throw new NotSupportedException();
        }

        string IXmAdvertisementPreset.AdvertisementPresetGroup
        {
            get => AdPresetGroup;
            set => throw new NotSupportedException();
        }

        string IPreset.AdDisplayProfile
        {
            get => AdDisplayProfile;
            set => throw new NotSupportedException();
        }

        string IXmReviewManagerPreset.RateDialogProfile
        {
            get => RateDialogProfile;
            set => throw new NotSupportedException();
        }

        string IXmCompetitionPreset.CompetitionRewardProfile
        {
            get => CompetitionRewardProfile;
            set => throw new NotSupportedException();
        }

        IGameResourcesPresetClientIntegration IPreset.GameResources
        {
            get => GameResources;
            set => throw new NotSupportedException();
        }

        IUserBonusesPresetClientIntegration IPreset.UserBonuses
        {
            get => UserBonuses;
            set => throw new NotSupportedException();
        }

        string ILegacyv1Preset.Legacyv1LevelMapProfile
        {
            get => LevelMapProfile;
            set => throw new NotSupportedException();
        }

        IUserRewardsPresetClientIntegration ILegacyv1Preset.Legacyv1UserRewards
        {
            get => UserRewards;
            set => throw new NotSupportedException();
        }

        string IPreset.CampaignLevelDesignProfile
        {
            get => LevelDesignProfile;
            set => throw new NotSupportedException();
        }

        IGameplayPresetClientIntegration IPreset.Gameplay
        {
            get => Gameplay;
            set => throw new NotSupportedException();
        }

        IUserTutorialsPresetClientIntegration IPreset.Tutorials
        {
            get => Tutorials;
            set => throw new NotSupportedException();
        }

        IMainMenuPresetClientIntegration IPreset.MainMenu
        {
            get => MainMenu;
            set => throw new NotSupportedException();
        }

        string IPreset.AchievementProfile
        {
            get => AchievementProfile;
            set => throw new NotSupportedException();
        }

        string IPreset.LeaderboardProfile
        {
            get => LeaderboardProfile;
            set => throw new NotSupportedException();
        }

        IUIPresetClientIntegration IPreset.UI
        {
            get => UI;
            set => throw new NotSupportedException();
        }

        string ILegacyv1Preset.Legacyv1PromoOfferProfile
        {
            get => PromoOfferProfile;
            set => throw new NotSupportedException();
        }

        string ILegacyv1Preset.Legacyv1PromoOfferViewDesignExperiment
        {
            get => PromoOfferViewDesignExperiment;
            set => throw new NotSupportedException();
        }

        string IXmPromoOfferPreset.PromoOfferViewDesignExperiment
        {
            get => PromoOfferViewDesignExperiment_v2;
            set => throw new NotSupportedException();
        }

        string IPreset.ImageCollectionProfile
        {
            get => ImageCollectionProfile;
            set => throw new NotSupportedException();
        }

        string IPreset.ImageCollectionAdDisplayProfile
        {
            get => GameChallengeAdDisplayProfile;
            set => throw new NotSupportedException();
        }

        ISkipItSettingsClientIntegration IPreset.SkipIt
        {
            get => SkipIt;
            set => throw new NotSupportedException();
        }

        ILevelRewardDesignPresetClientIntegration IPreset.LevelRewardDesign
        {
            get => LevelRewardDesign;
            set => throw new NotSupportedException();
        }

        string IXmUserAchievementPreset.UserAchievementBalanceProfile
        {
            get => AchievementBalanceProfile;
            set => throw new NotSupportedException();
        }

        string ILegacyv1Preset.Legacyv1GameChallengeSequence
        {
            get => GameChallengeProfile;
            set => throw new NotSupportedException();
        }

        string IQuestionDeliveryPreset.QuestionIssuePresetGroup
        {
            get => QuestionIssuePresetGroup;
            set => throw new NotSupportedException();
        }

        IXmExperimentalFeatureReferenceListClientIntegration IXmExperimentalFeaturePreset.ExperimentalFeatures
        {
            get => null;
            set => throw new NotSupportedException();
        }

        string ILegacyv1Preset.Legacyv1BonusLevelGameChallengeSequence
        {
            get => BonusLevelGameChallengeSequence;
            set => throw new NotSupportedException();
        }

        string IQuestionDeliveryPreset.TriviaMatrixQuestionProfile
        {
            get => TriviaMatrixQuestionProfile;
            set => throw new NotSupportedException();
        }

        string IPreset.BonusLevelAdDisplayProfile
        {
            get => BonusLevelAdDisplayProfile;
            set => throw new NotSupportedException();
        }

        string IPreset.OnboardingProfile
        {
            get => OnboardingProfile;
            set => throw new NotSupportedException();
        }

        IXmGameActivityScenarioReferenceListClientIntegration IXmGameActivityPreset.GameActivityScenarios
        {
            get => new XmGameActivityScenarioReferenceList(GameActivities.Concat(Offers));
            set => throw new NotSupportedException();
        }

        IBrainScoreSettingsClientIntegration IPreset.BrainScore
        {
            get => BrainScore;
            set => throw new NotSupportedException();
        }

        string IPreset.HapticFeedbackProfile
        {
            get => HapticFeedbackProfile;
            set => throw new NotSupportedException();
        }

        string IPreset.CampaignProfile
        {
            get => CampaignProfile;
            set => throw new NotSupportedException();
        }

        string IPreset.GameActivityAdDisplayProfile
        {
            get => GameActivityAdDisplayProfile;
            set => throw new NotSupportedException();
        }

        IGameActivityIconSettingsClientIntegration IPreset.GameActivityIconSettings
        {
            get => GameActivitiesIconSettings;
            set => throw new NotSupportedException();
        }
    }
}