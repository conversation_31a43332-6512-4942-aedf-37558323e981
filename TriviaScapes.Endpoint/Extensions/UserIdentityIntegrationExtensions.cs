using System;
using Xm.Users.Integration.Contracts;
using Xm.Utils.Runtime;

namespace TriviaScapes.Endpoint.Extensions;

internal static class UserIdentityIntegrationExtensions
{
    public static int GetCreatedOnUnixTimestamp(this IXmUserIdentityIntegration userIdentity)
    {
        return (int)new DateTimeOffset(userIdentity.CreatedOn).ToUnixTimeSeconds();
    }

    public static int GetRemainderOfDividingPublicId(this IXmUserIdentityIntegration userIdentity, int divider)
    {
        var publicId = userIdentity.PublicId;

        if (string.IsNullOrEmpty(publicId))
        {
            return 0;
        }

        var normalizePublicId = publicId;
        if (!ApplicationRuntimeInfo.Current.IsProduction())
        {
            normalizePublicId = publicId[..^1];
        }

        return int.TryParse(normalizePublicId, out var number) ? number % divider : 0;
    }
}