using Xm.Advertisement;
using Xm.Advertisement.Commands.CommandHandlers;
using Xm.Commands;

namespace TriviaScapes.Endpoint.CommandHandlers
{
	internal class AdvertisementGetPresetCommandHandler : XmAdvertisementGetPresetCommandHandler<CommandContext>
	{
		public AdvertisementGetPresetCommandHandler(IXmCommandContainer commandContainer, CommandContext context, IXmAdvertisementIntegration advertisement)
			: base(commandContainer, context, advertisement)
		{
		}
	}
}