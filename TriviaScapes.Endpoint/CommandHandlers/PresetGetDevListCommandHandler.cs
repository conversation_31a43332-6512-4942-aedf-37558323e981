using Xm.Commands;
using Xm.Presets.Commands.CommandHandlers;
using Xm.Presets.Selection;
using TriviaScapes.Endpoint.Presets;

namespace TriviaScapes.Endpoint.CommandHandlers
{
    internal class PresetGetDevListCommandHandler : XmPresetGetDevListCommandHandler<IPreset, PresetCondition, PresetModificationCondition, CommandContext>
    {
        public PresetGetDevListCommandHandler(IXmCommandContainer commandContainer, CommandContext context, IXmPresetSelector<IPreset, PresetCondition, PresetModificationCondition> presetSelector)
            : base(commandContainer, context, presetSelector)
        {
        }
    }
}