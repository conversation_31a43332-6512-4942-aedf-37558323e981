using Xm.Commands;
using Xm.Gameplays;
using Xm.Gameplays.Commands.CommandHandlers;

namespace TriviaScapes.Endpoint.CommandHandlers
{
    public class GetGameplayListCommandCommandHandler : XmGetGameplayListCommandCommandHandler<CommandContext>
    {
        public GetGameplayListCommandCommandHandler(IXmCommandContainer commandContainer, CommandContext context, IXmGameplayIntegration gameplays)
            : base(commandContainer, context, gameplays)
        {
        }
    }
}