using Xm.Commands;
using Xm.UserRewards;
using Xm.UserRewards.Commands.CommandHandlers;

namespace TriviaScapes.Endpoint.CommandHandlers
{
    public class GetUserRewardListCommandHandler : XmGetUserRewardListCommandHandler<CommandContext>
    {
        public GetUserRewardListCommandHandler(IXmCommandContainer commandContainer, CommandContext context, IXmUserRewardIntegration rewards)
            : base(commandContainer, context, rewards)
        {
        }
    }
}