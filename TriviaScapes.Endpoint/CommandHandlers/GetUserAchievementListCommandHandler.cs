using Xm.Commands;
using Xm.UserAchievements;
using Xm.UserAchievements.Commands.CommandHandlers;

namespace TriviaScapes.Endpoint.CommandHandlers
{
    public class GetUserAchievementListCommandHandler : XmGetUserAchievementListCommandHandler<CommandContext>
    {
        public GetUserAchievementListCommandHandler(IXmCommandContainer commandContainer, CommandContext context, IXmUserAchievementIntegration userAchievementIntegration)
            : base(commandContainer, context, userAchievementIntegration)
        { }
    }
}