using Xm.Advertisement;
using Xm.Advertisement.Commands.CommandHandlers;
using Xm.Commands;

namespace TriviaScapes.Endpoint.CommandHandlers
{
	internal class AdvertisementGetPresetListCommandHandler : XmAdvertisementGetPresetListCommandHandler<CommandContext>
	{
		public AdvertisementGetPresetListCommandHandler(IXmCommandContainer commandContainer, CommandContext context, IXmAdvertisementIntegration advertisement)
			: base(commandContainer, context, advertisement)
		{
		}
	}
}