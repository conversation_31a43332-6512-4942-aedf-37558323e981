using System.Threading;
using System.Threading.Tasks;
using TriviaScapes.ClientIntegration.Contracts.HapticFeedback;
using TriviaScapes.Endpoint.HapticFeedback;
using Xm.AppSettings.Abstractions;
using Xm.Commands;
using Xm.Commands.Abstractions;

namespace TriviaScapes.Endpoint.CommandHandlers
{
    internal class GetHapticFeedbackProfileCommandHandler : CommandHandler<IGetHapticFeedbackProfileCommand>
    {
        private readonly IHapticFeedbackProfileRepository _profileRepository;

        public GetHapticFeedbackProfileCommandHandler(
            IXmCommandContainer commandContainer,
            CommandContext context,
            IHapticFeedbackProfileRepository profileRepository
        )
            : base(commandContainer, context)
        {
            _profileRepository = profileRepository;
        }

        protected override Task<IXmCommandResult> HandleInternalAsync(CancellationToken cancellationToken)
        {
            var result = new GetHapticFeedbackProfileCommandResultDto();

            var profile = _profileRepository.Get(Command.ProfileName, Context.ClientAppStart.AppVersion);
            if (profile.Changed(Command.CurrentVersion))
            {
                result.Profile = new XmAppSettings__HapticFeedbackProfileClientIntegrationDtoDto(profile);
            }

            return Task.FromResult<IXmCommandResult>(result);
        }
    }
}