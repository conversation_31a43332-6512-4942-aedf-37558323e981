using System.Threading;
using System.Threading.Tasks;
using TriviaScapes.ClientIntegration.Contracts.Onboarding;
using TriviaScapes.Endpoint.Onboarding;
using Xm.AppSettings.Abstractions;
using Xm.Commands;
using Xm.Commands.Abstractions;

namespace TriviaScapes.Endpoint.CommandHandlers
{
    internal class GetOnboardingProfileCommandHandler : CommandHandler<IGetOnboardingProfileCommand>
    {
        private readonly IOnboardingProfileRepository _profileRepository;

        public GetOnboardingProfileCommandHandler(
            IXmCommandContainer commandContainer,
            CommandContext context,
            IOnboardingProfileRepository profileRepository
        )
            : base(commandContainer, context)
        {
            _profileRepository = profileRepository;
        }

        protected override Task<IXmCommandResult> HandleInternalAsync(CancellationToken cancellationToken)
        {
            var result = new GetOnboardingProfileCommandResultDto();

            var profile = _profileRepository.Get(Command.ProfileName, Context);
            if (profile.Changed(Command.CurrentVersion))
            {
                result.Profile = new XmAppSettings__OnboardingProfileClientIntegrationDtoDto(profile);
            }

            return Task.FromResult<IXmCommandResult>(result);
        }
    }
}