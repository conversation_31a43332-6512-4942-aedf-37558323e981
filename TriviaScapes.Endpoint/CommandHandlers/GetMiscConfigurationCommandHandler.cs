using System.Threading;
using System.Threading.Tasks;
using TriviaScapes.ClientIntegration.Contracts.Misc;
using TriviaScapes.Endpoint.Misc;
using Xm.AppSettings.Abstractions;
using Xm.Commands;
using Xm.Commands.Abstractions;

namespace TriviaScapes.Endpoint.CommandHandlers
{
    public class GetMiscConfigurationCommandHandler : XmCommandHandler<IGetMiscConfigurationCommand, CommandContext>
    {
        private readonly IMiscConfigurationRepository _settingsRepository;

        public GetMiscConfigurationCommandHandler(
            IXmCommandContainer commandContainer,
            CommandContext context,
            IMiscConfigurationRepository settingsRepository
        ) : base(commandContainer, context)
        {
            _settingsRepository = settingsRepository;
        }

        protected override Task<IXmCommandResult> HandleInternalAsync(CancellationToken cancellationToken)
        {
            var result = new GetMiscConfigurationCommandResultDto();

            var settings = _settingsRepository.Get(Context.DeviceAppStoreVendor, Context.ClientAppStart.AppVersion);
            if (settings.Changed(Command.CurrentVersion))
            {
                result.Misc = new XmAppSettings__MiscConfigurationClientIntegrationDtoDto(settings);
            }

            return Task.FromResult<IXmCommandResult>(result);
        }
    }
}