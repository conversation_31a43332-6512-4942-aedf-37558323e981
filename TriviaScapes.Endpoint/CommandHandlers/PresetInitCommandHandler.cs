using System.Threading.Tasks;
using TriviaScapes.ClientIntegration.Contracts.BrainScore;
using TriviaScapes.ClientIntegration.Contracts.GameActivities.Icons;
using TriviaScapes.ClientIntegration.Contracts.Presets;
using TriviaScapes.ClientIntegration.Contracts.Presets.Gameplay;
using TriviaScapes.ClientIntegration.Contracts.Presets.GameResources;
using TriviaScapes.ClientIntegration.Contracts.Presets.LevelRewardDesign;
using TriviaScapes.ClientIntegration.Contracts.Presets.MainMenu;
using TriviaScapes.ClientIntegration.Contracts.Presets.ReviewManager;
using TriviaScapes.ClientIntegration.Contracts.Presets.SkipIt;
using TriviaScapes.ClientIntegration.Contracts.Presets.Tutorials;
using TriviaScapes.ClientIntegration.Contracts.Presets.UI;
using TriviaScapes.ClientIntegration.Contracts.Presets.UserBonuses;
using TriviaScapes.ClientIntegration.Contracts.Presets.UserRewards;
using TriviaScapes.Endpoint.Presets;
using Xm.AppSettings.Abstractions;
using Xm.Commands;
using Xm.Contracts;
using Xm.ExperimentalFeatures.ClientIntegration.Contracts.References;
using Xm.GameActivities.ClientIntegration.Contracts.Scenarios;
using Xm.Presets.Commands.Abstractions;
using Xm.Presets.Commands.CommandHandlers;
using Xm.Presets.Modifications;
using Xm.Presets.Selection;

namespace TriviaScapes.Endpoint.CommandHandlers
{
    internal class PresetInitCommandHandler : XmPresetInitCommandHandler<IPreset, PresetCondition, PresetModificationCondition, IPresetClientIntegration, CommandContext>
    {
        public PresetInitCommandHandler(
            IXmCommandContainer commandContainer,
            CommandContext context,
            IXmPresetSelector<IPreset, PresetCondition, PresetModificationCondition> presetSelector
        )
            : base(commandContainer, context, presetSelector)
        {
        }

        protected override IXmPresetInitCommandResult<IPresetClientIntegration> CreateResult(IXmAppSettings<IPresetClientIntegration> preset)
        {
            return new PresetInitCommandResultDto()
            {
                Preset = preset == null ? null : new XmAppSettings__PresetClientIntegrationDtoDto(preset)
            };
        }

        protected override Task<IXmAppSettings<IPresetClientIntegration>> GetClientPresetAsync(IXmModifiedPresetContainer<IPreset> preset)
        {
            var clientPreset = new PresetClientIntegrationDto()
            {
                Identifier = new Xm.Presets.Abstractions.XmPresetIdentifierDto(preset.Identifier),
                AdDisplayProfile = preset.Value.AdDisplayProfile,
                AdPresetGroup = preset.Value.AdvertisementPresetGroup,
                Store = preset.Value.Store,
                RateDialogProfile = preset.Value.RateDialogProfile,
                GameResources = new GameResourcesPresetClientIntegrationDto(preset.Value.GameResources),
                Legacyv1LevelMapProfile = preset.Value.Legacyv1LevelMapProfile,
                UserBonuses = new UserBonusesPresetClientIntegrationDto(preset.Value.UserBonuses),
                Legacyv1UserRewards = new UserRewardsPresetClientIntegrationDto(preset.Value.Legacyv1UserRewards),
                CampaignLevelDesignProfile = preset.Value.CampaignLevelDesignProfile,
                Gameplay = new GameplayPresetClientIntegrationDto(preset.Value.Gameplay),
                Tutorials = new UserTutorialsPresetClientIntegrationDto(preset.Value.Tutorials),
                LocalizationTest = preset.Value.LocalizationTest,
                LeaderboardProfile = preset.Value.LeaderboardProfile,
                AchievementProfile = preset.Value.AchievementProfile,
                CompetitionRewardProfile = preset.Value.CompetitionRewardProfile,
                MainMenu = new MainMenuPresetClientIntegrationDto(preset.Value.MainMenu),
                AchievementBalanceProfile = preset.Value.UserAchievementBalanceProfile,
                UI = new UIPresetClientIntegrationDto(preset.Value.UI),
                Legacyv1PromoOfferProfile = preset.Value.Legacyv1PromoOfferProfile,
                Legacyv1PromoOfferViewDesignExperiment = preset.Value.Legacyv1PromoOfferViewDesignExperiment,
                ImageCollectionProfile = preset.Value.ImageCollectionProfile,
                Legacyv1GameChallengeProfile = preset.Value.Legacyv1GameChallengeSequence,
                ImageCollectionAdDisplayProfile = preset.Value.ImageCollectionAdDisplayProfile,
                RateDialogDesign = new RateDialogDesignPresetClientIntegrationDto
                {
                    Kind = RateDialogDesignKind.SubmitButton,
                    NativeRateMode = RateDialogNativeRateMode.ShowNativeDialog,
                },
                LevelRewardDesign = new LevelRewardDesignPresetClientIntegrationDto(preset.Value.LevelRewardDesign),
                ExperimentalFeatures = preset.Value.ExperimentalFeatures == null ? null : new XmExperimentalFeatureReferenceListClientIntegrationDto(preset.Value.ExperimentalFeatures),
                SkipIt = new SkipItSettingsClientIntegrationDto(preset.Value.SkipIt),
                Legacyv1BonusLevelGameChallengeSequence = preset.Value.Legacyv1BonusLevelGameChallengeSequence,
                BonusLevelAdDisplayProfile = preset.Value.BonusLevelAdDisplayProfile,
                OnboardingProfile = preset.Value.OnboardingProfile,
                GameActivityScenarios = preset.Value.GameActivityScenarios == null ? null : new XmGameActivityScenarioReferenceListClientIntegrationDto(preset.Value.GameActivityScenarios),
                BrainScore = preset.Value.BrainScore == null ? null : new BrainScoreSettingsClientIntegrationDto(preset.Value.BrainScore),
                HapticFeedbackProfile = preset.Value.HapticFeedbackProfile,
                CampaignProfile = preset.Value.CampaignProfile,
                GameActivityAdDisplayProfile = preset.Value.GameActivityAdDisplayProfile,
                PromoOfferViewDesignExperiment = preset.Value.PromoOfferViewDesignExperiment,
                GameActivityIconSettings = preset.Value.GameActivityIconSettings == null ? null : new GameActivityIconSettingsClientIntegrationDto(preset.Value.GameActivityIconSettings)
            };

            return Task.FromResult<IXmAppSettings<IPresetClientIntegration>>(
                new XmAppSettings<IPresetClientIntegration>(
                    Context.ClientAppStart.AppVersion,
                    Dto.SerializableContractRepository.Instance.CalculateVersion<IPresetClientIntegration>(clientPreset),
                    clientPreset
                )
            );
        }
    }
}