using Xm.Commands;
using Xm.ExperimentalFeatures;
using Xm.ExperimentalFeatures.Commands.CommandHandlers;

namespace TriviaScapes.Endpoint.CommandHandlers
{
    internal class GetExperimentalFeatureListCommandHandler : XmGetExperimentalFeatureListCommandHandler<CommandContext>
    {
        public GetExperimentalFeatureListCommandHandler(IXmCommandContainer commandContainer, CommandContext context, IXmExperimentalFeatureIntegration experimentalFeatures)
            : base(commandContainer, context, experimentalFeatures)
        {
        }
    }
}