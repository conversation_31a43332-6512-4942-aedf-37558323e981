using System.Threading;
using System.Threading.Tasks;
using TriviaScapes.ClientIntegration.Contracts.Leaderboards;
using TriviaScapes.Endpoint.Leaderboards;
using Xm.AppSettings.Abstractions;
using Xm.Commands;
using Xm.Commands.Abstractions;

namespace TriviaScapes.Endpoint.CommandHandlers
{
    internal class GetLeaderboardProfileCommandHandler : CommandHandler<IGetLeaderboardProfileCommand>
    {
        private readonly ILeaderboardProfileRepository _profileRepository;

        public GetLeaderboardProfileCommandHandler(
            IXmCommandContainer commandContainer,
            CommandContext context,
            ILeaderboardProfileRepository profileRepository
        )
            : base(commandContainer, context)
        {
            _profileRepository = profileRepository;
        }

        protected override Task<IXmCommandResult> HandleInternalAsync(CancellationToken cancellationToken)
        {
            var result = new GetLeaderboardProfileCommandResultDto();

            var profile = _profileRepository.Get(Command.ProfileName);
            if (profile.Changed(Command.CurrentVersion))
            {
                result.Profile = new XmAppSettings__LeaderboardProfileClientIntegrationDtoDto(profile);
            }

            return Task.FromResult<IXmCommandResult>(result);
        }
    }
}