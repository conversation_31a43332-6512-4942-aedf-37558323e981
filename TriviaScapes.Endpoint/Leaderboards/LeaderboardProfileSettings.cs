using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts.Leaderboards;

namespace TriviaScapes.Endpoint.Leaderboards
{
    internal class LeaderboardProfileSettings : ILeaderboardProfileClientIntegration
    {
        [ConfigUnique]
        [ConfigDisplayStyle(IsHeader = true)]
        [ConfigCollectionDisplayKey]
        [ConfigCollectionKey]
        public string Name { get; private set; }

        [ConfigDisplayStyle(IsHeader = true)]
        [Description("UnitySocial - Apple Game Center/Google Play Game Services")]
        public LeaderboardProvider Provider { get; private set; }

        [ConfigDisplayStyle(IsHeader = true)]
        public string LeaderboardId { get; private set; }
    }
}