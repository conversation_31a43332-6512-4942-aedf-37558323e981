using System.Collections.Generic;
using System.Linq;
using Lr.Basic.Config.TreeObjects;
using Lr.Basic.Config.Variants;

namespace TriviaScapes.Endpoint.Leaderboards
{
    internal class LeaderboardProfileConfigVariantSource : IConfigContextVariantSource
    {
        public const string VariantName = "LeaderboardProfiles";

        string IConfigContextVariantSource.VariantName => VariantName;

        public IEnumerable<VariantDescription> GetVariants(IConfigVariantContext context)
        {
            return context.Get<LeaderboardConfiguration, LeaderboardProfileSettings[]>(c => c.Profiles)?
                          .Select(p => new VariantDescription()
                          {
                              Value = p.Name,
                              DisplayName = p.Name,
                              Name = p.Name
                          }) ?? Enumerable.Empty<VariantDescription>();
        }
    }
}