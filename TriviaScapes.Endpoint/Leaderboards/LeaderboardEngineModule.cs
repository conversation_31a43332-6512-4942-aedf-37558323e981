using Autofac;
using Xm.ApplicationEngine;
using Xm.ApplicationEngine.Config;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.Endpoint.Leaderboards
{
    internal class LeaderboardEngineModule : EngineModule
    {
        private readonly ISerializableContractRepository _serializableContractRepository;

        public LeaderboardEngineModule(ISerializableContractRepository serializableContractRepository)
        {
            _serializableContractRepository = serializableContractRepository;
        }

        public override void RegisterConfigTypes(IConfigStorageBuilder configStorageBuilder)
        {
            base.RegisterConfigTypes(configStorageBuilder);

            configStorageBuilder.RegisterGameplayConfigType<LeaderboardConfiguration>();
            configStorageBuilder.RegisterGameplayVariantSource<LeaderboardProfileConfigVariantSource>();
        }

        protected override void RegisterComponents(ContainerBuilder builder)
        {
            base.RegisterComponents(builder);

            builder.RegisterType<LeaderboardProfileConfigRepository>()
                   .WithParameter(TypedParameter.From(_serializableContractRepository))
                   .As<ILeaderboardProfileRepository>()
                   .SingleInstance();
        }
    }
}