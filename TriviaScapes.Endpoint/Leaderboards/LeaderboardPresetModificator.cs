using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.Endpoint.Presets;
using Xm.Presets.Modifications;

namespace TriviaScapes.Endpoint.Leaderboards
{
    [ConfigInherit]
    [DisplayName("Leaderboard")]
    internal class LeaderboardPresetModificator : IXmPresetModificator<IPreset>
    {
        [LeaderboardProfile]
        [ConfigDisplayStyle(IsHeader = true)]
        public string LeaderboardProfile { get; private set; }

        public void Modify(IPreset preset)
        {
            preset.LeaderboardProfile = LeaderboardProfile;
        }
    }
}