using System;
using System.Collections.Generic;
using System.Linq;
using TriviaScapes.ClientIntegration.Contracts.Leaderboards;
using Xm.AppSettings.Abstractions;
using Xm.Caching;
using Xm.Contracts;
using Xm.Contracts.Abstractions;
using Xm.Utils;
using Xm.Utils.ChangeableObjects;

namespace TriviaScapes.Endpoint.Leaderboards
{
    internal class LeaderboardProfileConfigRepository : ILeaderboardProfileRepository, IDisposable
    {
        private readonly IConvertedChangeableObject<IDictionary<string, ILeaderboardProfileClientIntegration>> _profiles;
        private readonly ISerializableContractRepository _serializableContractRepository;

        public LeaderboardProfileConfigRepository(
            IChangeableObject<LeaderboardConfiguration> configuration,
            ISerializableContractRepository serializableContractRepository
        )
        {
            _profiles = configuration.CreateConverted(c =>
                c.Profiles?.ToDictionary<LeaderboardProfileSettings, string, ILeaderboardProfileClientIntegration>(
                    p => p.Name,
                    p => p,
                    StringComparer.OrdinalIgnoreCase
                ) ?? new Dictionary<string, ILeaderboardProfileClientIntegration>()
            );

            _serializableContractRepository = serializableContractRepository;
        }

        public void Dispose()
        {
            _profiles?.Dispose();
        }

        public IXmAppSettings<ILeaderboardProfileClientIntegration> Get(string name)
        {
            if (!_profiles.Value.TryGetValue(name, out var profile))
            {
                throw new InvalidOperationException("Profile is not found")
                    .SetData("Name", name);
            }

            return new XmAppSettings<ILeaderboardProfileClientIntegration>(_serializableContractRepository.CalculateVersion(profile), profile);
        }
    }
}