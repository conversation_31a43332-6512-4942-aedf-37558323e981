using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts.Billing.Products;

namespace TriviaScapes.Endpoint.Billing.Products
{
    [ConfigInherit]
    [DisplayName("Quiz Blitz.Not Enough Time")]
    public class QuizBlitzTimeProductSettings : InAppProductSettings<IQuizBlitzTimeProductParametersClientIntegration>, IQuizBlitzTimeProductParametersClientIntegration
    {
        protected override InAppProductCategory Category => InAppProductCategory.QuizBlitzNotEnough;

        protected override IQuizBlitzTimeProductParametersClientIntegration Parameters => this;

        [Description("In seconds")]
        public int RestoreTimeAmount { get; private set; }

        public object Clone()
        {
            return new QuizBlitzTimeProductSettings
            {
                RestoreTimeAmount = RestoreTimeAmount
            };
        }
    }
}