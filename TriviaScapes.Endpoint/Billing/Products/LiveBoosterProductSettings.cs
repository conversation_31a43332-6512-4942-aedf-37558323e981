using System;
using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts.Billing.Products;
using TriviaScapes.Endpoint.Billing.Products.Utils;
using Xm.GameLives.ClientIntegration.Contracts;

namespace TriviaScapes.Endpoint.Billing.Products
{
    [ConfigInherit]
    [DisplayName("Live Booster")]
    internal class LiveBoosterProductSettings : InAppProductSettings<ILiveBoosterProductParametersClientIntegration>, ILiveBoosterProductParametersClientIntegration
    {
        protected override InAppProductCategory Category => InAppProductCategory.LiveBooster;

        protected override ILiveBoosterProductParametersClientIntegration Parameters => this;

        [ConfigDisplayStyle(IsHeader = true)]
        public XmLiveBoosterKind Kind { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline, IsHeader = true)]
        public ProductEffectDurationSettings Duration { get; private set; }

        TimeSpan ILiveBoosterProductParametersClientIntegration.Duration => Duration.GetTimeSpan();

        public object Clone()
        {
            return new LiveBoosterProductSettings
            {
                Kind = Kind,
                Duration = Duration,
            };
        }
    }
}