using System;
using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;

namespace TriviaScapes.Endpoint.Billing.Products.Utils
{
    public class ProductEffectDurationSettings
    {
        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline, IsHeader = true)]
        [DefaultValue(ProductEffectDurationTimeUnit.Hour)]
        public ProductEffectDurationTimeUnit Unit { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline, IsHeader = true)]
        [DefaultValue(24)]
        public int Value { get; private set; }

        public TimeSpan GetTimeSpan()
        {
            switch (Unit)
            {
                case ProductEffectDurationTimeUnit.Minute:
                    return TimeSpan.FromMinutes(Value);

                case ProductEffectDurationTimeUnit.Hour:
                    return TimeSpan.FromHours(Value);

                case ProductEffectDurationTimeUnit.Day:
                    return TimeSpan.FromDays(Value);

                default:
                    throw new InvalidEnumArgumentException(nameof(Unit), (int) Unit, typeof(ProductEffectDurationTimeUnit));
            }
        }
    }
}