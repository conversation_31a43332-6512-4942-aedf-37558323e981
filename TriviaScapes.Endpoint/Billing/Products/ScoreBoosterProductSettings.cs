using System;
using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts.Billing.Products;
using TriviaScapes.ClientIntegration.Contracts.Inventory;
using TriviaScapes.Endpoint.Billing.Products.Utils;

namespace TriviaScapes.Endpoint.Billing.Products
{
    [ConfigInherit]
    [DisplayName("Score Booster")]
    internal class ScoreBoosterProductSettings : InAppProductSettings<IScoreBoosterProductClientIntegration>, IScoreBoosterProductClientIntegration
    {
        protected override InAppProductCategory Category => InAppProductCategory.ScoreBooster;

        protected override IScoreBoosterProductClientIntegration Parameters => this;

        [ConfigDisplayStyle(IsHeader = true)]
        public ScoreBoosterKind Kind { get; private set; }


        [ConfigDisplayStyle(IsHeader = true)]
        public ProductEffectDurationSettings Duration { get; private set; }

        TimeSpan IScoreBoosterProductClientIntegration.Duration => Duration.GetTimeSpan();

        public object Clone()
        {
            return new ScoreBoosterProductSettings
            {
                Kind = Kind,
                Duration = Duration,
            };
        }
    }
}