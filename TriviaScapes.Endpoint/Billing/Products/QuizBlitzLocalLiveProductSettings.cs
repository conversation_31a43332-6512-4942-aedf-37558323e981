using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts.Billing.Products;

namespace TriviaScapes.Endpoint.Billing.Products
{
    [ConfigInherit]
    [DisplayName("Quiz Blitz.Not Enough Local Lives")]
    public class QuizBlitzLocalLiveProductSettings : InAppProductSettings<IQuizBlitzLocalLiveProductParametersClientIntegration>, IQuizBlitzLocalLiveProductParametersClientIntegration
    {
        protected override InAppProductCategory Category => InAppProductCategory.QuizBlitzNotEnough;

        protected override IQuizBlitzLocalLiveProductParametersClientIntegration Parameters => this;

        [ConfigDisplayStyle(IsHeader = true)]
        public int RestoreLivesAmount { get; private set; }

        public object Clone()
        {
            return new QuizBlitzLocalLiveProductSettings
            {
                RestoreLivesAmount = RestoreLivesAmount
            };
        }
    }
}