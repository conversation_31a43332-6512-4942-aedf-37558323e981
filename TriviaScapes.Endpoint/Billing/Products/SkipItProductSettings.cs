using System.ComponentModel;
using TriviaScapes.ClientIntegration.Contracts.Billing.Products;
using Lr.Basic.Config.Attributes;

namespace TriviaScapes.Endpoint.Billing.Products;

[ConfigInherit]
[DisplayName("SkipIt")]
public class SkipItProductSettings : InAppProductSettings<ISkipItProductParametersClientIntegration>, ISkipItProductParametersClientIntegration
{
    [ConfigDisplayStyle(DisplayStyle = Lr.Basic.Config.Data.DisplayStyle.Inline)]
    public int Count { get; set; }

    protected override InAppProductCategory Category => InAppProductCategory.SkipIt;

    protected override ISkipItProductParametersClientIntegration Parameters => this;

    public object Clone()
    {
        return new SkipItProductSettings
        {
            Count = Count
        };
    }
}