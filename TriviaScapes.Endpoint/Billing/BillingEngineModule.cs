using Autofac;
using TriviaScapes.ClientIntegration.Contracts.Billing.Products;
using TriviaScapes.Endpoint.Billing.Stores;
using Xm.ApplicationEngine.Config;
using Xm.Billing.Registration;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.Endpoint.Billing
{
    internal class BillingEngineModule : XmBillingEngineModule
    {
        private readonly ISerializableContractRepository _serializableContractRepository;

        public BillingEngineModule(ISerializableContractRepository serializableContractRepository)
            : base(serializableContractRepository, b => b.AddGameActivityAttempts().AddProductCategories(except: InAppProductCategory.None))
        {
            _serializableContractRepository = serializableContractRepository;
        }

        public override void RegisterConfigTypes(IConfigStorageBuilder configStorageBuilder)
        {
            base.RegisterConfigTypes(configStorageBuilder);

            configStorageBuilder.RegisterGameplayConfigType<BillingConfiguration>();
            configStorageBuilder.RegisterGameplayVariantSource<StoreConfigVariantSource>();
        }

        protected override void RegisterComponents(ContainerBuilder builder)
        {
            base.RegisterComponents(builder);

            builder.RegisterType<StoreConfigRepository>()
                   .WithParameter(TypedParameter.From(_serializableContractRepository))
                   .As<IStoreRepository>()
                   .SingleInstance();
        }
    }
}