using TriviaScapes.ClientIntegration.Contracts.Billing.Products;
using TriviaScapes.Endpoint.Billing.Prices;
using TriviaScapes.Endpoint.Billing.Products;

namespace TriviaScapes.Endpoint.Billing.Stores.Products;

public class PremiumSpinProductSettings : InAppProductPriceTagSettings
{
    [InAppProduct(category: InAppProductCategory.PremiumSpin)]
    public override string ProductId { get; protected set; }
}