using TriviaScapes.ClientIntegration.Contracts.Billing.Products;
using TriviaScapes.Endpoint.Billing.Products;
using Xm.Billing.Prices;

namespace TriviaScapes.Endpoint.Billing.Stores.Products
{
    internal class StoreSkipItProductSettings : XmInAppProductPriceTagSettings
    {
        [InAppProduct(category: InAppProductCategory.SkipIt)]
        public override string ProductId { get; protected set; }
    }
}