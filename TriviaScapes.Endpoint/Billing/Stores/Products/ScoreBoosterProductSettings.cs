using TriviaScapes.ClientIntegration.Contracts.Billing.Products;
using TriviaScapes.Endpoint.Billing.Prices;
using TriviaScapes.Endpoint.Billing.Products;

namespace TriviaScapes.Endpoint.Billing.Stores.Products
{
    internal class ScoreBoosterProductSettings : InAppProductPriceTagSettings
    {
        [InAppProduct(category: InAppProductCategory.ScoreBooster)]
        public override string ProductId { get; protected set; }
    }
}