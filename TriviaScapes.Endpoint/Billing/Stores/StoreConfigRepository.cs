using System;
using System.Collections.Generic;
using System.Linq;
using TriviaScapes.ClientIntegration.Contracts.Billing.Stores;
using Xm.AppSettings.Abstractions;
using Xm.Billing;
using Xm.Billing.ClientIntegration.Contracts.Prices;
using Xm.Billing.Prices;
using Xm.Caching;
using Xm.Contracts;
using Xm.Contracts.Abstractions;
using Xm.Utils;
using Xm.Utils.ChangeableObjects;

namespace TriviaScapes.Endpoint.Billing.Stores
{
    internal class StoreConfigRepository : IStoreRepository, IDisposable
    {
        private readonly IConvertedChangeableObject<IDictionary<string, IStoreClientIntegration>> _stores;
        private readonly ISerializableContractRepository _serializableContractRepository;

        public StoreConfigRepository(
            IChangeableObject<BillingConfiguration> configuration,
            IXmBillingIntegration billing,
            ISerializableContractRepository serializableContractRepository
        )
        {
            _stores = configuration.CreateConverted(
                c =>
                    c.Stores?.ToDictionary(
                        s => s.Name,
                        s => CreateStore(billing, s),
                        StringComparer.OrdinalIgnoreCase
                    )
                 ?? new Dictionary<string, IStoreClientIntegration>()
            );

            _serializableContractRepository = serializableContractRepository;
        }

        public IXmAppSettings<IStoreClientIntegration> Get(string name, int appVersion)
        {
            if (!_stores.Value.TryGetValue(name, out var store))
            {
                throw new InvalidOperationException("Store is not found")
                   .SetData("Name", name);
            }

            return new XmAppSettings<IStoreClientIntegration>(appVersion, _serializableContractRepository.CalculateVersion(store), store);
        }

        private static IStoreClientIntegration CreateStore(IXmBillingIntegration billing, StoreSettings store)
        {
            return new StoreClientIntegrationDto()
            {
                StoreProducts = store.StoreProducts?.Select(ConvertProductToDto).ToList(),
                FailedLevelExtraLifeProduct = ConvertProductToDto(store.FailedLevelExtraLifeProduct),
                HintProducts = store.HintProducts?.Select(ConvertProductToDto).ToList(),
                LevelEntranceProduct = ConvertProductToDto(store.LevelEntranceProduct),
                NotEnoughCoinsProduct = ConvertProductToDto(store.NotEnoughCoinsProduct),
                NotEnoughLivesProduct = ConvertProductToDto(store.NotEnoughLivesProduct),
                OfferProduct = ConvertProductToDto(store.OfferProduct),
                OfferOldPriceProduct = ConvertProductToDto(store.OfferOldPriceProduct),
                GameChallengeQuestionAttemptProduct = ConvertProductToDto(store.GameChallengeQuestionAttemptProduct),
                NotEnoughSkipItProduct = ConvertProductToDto(store.NotEnoughSkipItProduct),
                NotEnoughHintProducts = store.NotEnoughHintProducts?.Select(ConvertProductToDto).ToList(),
                NotEnoughLiveProductList = store.NotEnoughLiveProductList?.Select(ConvertProductToDto).ToList(),
                NotEnoughCoinProductList = store.NotEnoughCoinProductList?.Select(ConvertProductToDto).ToList(),
                NotEnoughSkipItProductList = store.NotEnoughSkipItProductList?.Select(ConvertProductToDto).ToList(),
                ExpiredScoreBoostersProductList = store.ExpiredScoreBoostersProductList?.Select(ConvertProductToDto).ToList(),
                ExpiredLiveBoostersProductList = store.ExpiredLiveBoostersProductList?.Select(ConvertProductToDto).ToList(),
                NotEnoughTimeProducts = store.NotEnoughTimeProducts?.Select(ConvertProductToDto).ToList(),
                NotEnoughLocalLivesProducts = store.NotEnoughLocalLivesProducts?.Select(ConvertProductToDto).ToList()
            };

            StoreProductClientIntegrationDto ConvertProductToDto(XmInAppProductPriceTagSettings product)
            {
                if (product?.Price == null)
                {
                    return null;
                }

                return new StoreProductClientIntegrationDto
                {
                    PriceTag = new XmInAppProductPriceTagClientIntegrationDto(product.ToClientIntegration(billing)),
                };
            }
        }

        public void Dispose()
        {
            _stores?.Dispose();
        }
    }
}