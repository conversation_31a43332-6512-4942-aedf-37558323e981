using System;
using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts;
using TriviaScapes.Endpoint.Billing.Stores.Products;

namespace TriviaScapes.Endpoint.Billing.Stores
{
    internal class StoreSettings
    {
        private const string NotEnoughTab = "Not Enough";

        [ConfigUnique]
        [ConfigCollectionKey]
        [ConfigCollectionDisplayKey]
        [ConfigDisplayStyle(IsHeader = true)]
        public string Name { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public StoreProductSettings[] StoreProducts { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public StoreProductSettings LevelEntranceProduct { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public StoreProductSettings OfferProduct { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public StoreProductSettings OfferOldPriceProduct { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public StoreProductSettings NotEnoughCoinsProduct { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public StoreProductSettings NotEnoughLivesProduct { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public StoreProductSettings FailedLevelExtraLifeProduct { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        [DisplayName("Gameplay Hint Products")]
        [Description("Задаём только HintProduct. Будет использоваться, если подсказка с таким типом не задана в NotEnoughHintProducts")]
        public StoreProductSettings[] HintProducts { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        [Obsolete("Since " + nameof(AppReleaseVersions.NewGameplayStructure))]
        [Description("Deprecated - replaced with gameplay activity attempts")]
        public StoreProductSettings GameChallengeQuestionAttemptProduct { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public StoreProductSettings NotEnoughSkipItProduct { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline, TabName = NotEnoughTab)]
        public StoreHintPackProductSettings[] NotEnoughHintProducts { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline, TabName = NotEnoughTab)]
        public StoreLiveProductSettings[] NotEnoughLiveProductList { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline, TabName = NotEnoughTab)]
        public StoreCoinProductSettings[] NotEnoughCoinProductList { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline, TabName = NotEnoughTab)]
        public StoreSkipItProductSettings[] NotEnoughSkipItProductList { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline, TabName = NotEnoughTab)]
        public ScoreBoosterProductSettings[] ExpiredScoreBoostersProductList { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline, TabName = NotEnoughTab)]
        public StoreLiveProductSettings[] ExpiredLiveBoostersProductList { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline, TabName = NotEnoughTab)]
        [Description("Время закончилось. Продукты на экране NotEnough для QuizBlitz")]
        public QuizBlitzNotEnoughProductSettings[] NotEnoughTimeProducts { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline, TabName = NotEnoughTab)]
        [Description("Локальные жизни закончились. Продукты на экране NotEnough для QuizBlitz")]
        public QuizBlitzNotEnoughProductSettings[] NotEnoughLocalLivesProducts { get; private set; }
    }
}