using Xm.Billing;
using Xm.Billing.Commands.CommandHandlers;
using Xm.Commands;

namespace TriviaScapes.Endpoint.Billing.CommandHandlers
{
	internal class GetInAppProductListCommandHandler : XmGetInAppProductListCommandHandler<CommandContext>
	{
		public GetInAppProductListCommandHandler(IXmCommandContainer commandContainer, CommandContext context, IXmBillingIntegration billing)
			: base(commandContainer, context, billing)
		{
		}
	}
}