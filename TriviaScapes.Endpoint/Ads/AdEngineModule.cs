using Autofac;
using TriviaScapes.Endpoint.Ads.DisplayProfiles;
using Xm.Advertisement;
using Xm.ApplicationEngine.Config;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.Endpoint.Ads
{
    internal class AdEngineModule : XmAdvertisementEngineModule
    {
        private readonly ISerializableContractRepository _serializableContractRepository;

        public AdEngineModule(ISerializableContractRepository serializableContractRepository)
            : base(serializableContractRepository)
        {
            _serializableContractRepository = serializableContractRepository;
        }

        public override void RegisterConfigTypes(IConfigStorageBuilder configStorageBuilder)
        {
            base.RegisterConfigTypes(configStorageBuilder);

            configStorageBuilder.RegisterGameplayConfigType<AdConfiguration>();
            configStorageBuilder.RegisterGameplayVariantSource<AdDisplayProfileConfigVariantSource>();
        }

        protected override void RegisterComponents(ContainerBuilder builder)
        {
            base.RegisterComponents(builder);

            builder.RegisterType<AdDisplayProfileConfigRepository>()
                   .WithParameter(TypedParameter.From(_serializableContractRepository))
                   .As<IAdDisplayProfileRepository>()
                   .SingleInstance();
        }
    }
}