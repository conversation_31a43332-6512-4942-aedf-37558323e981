using System;
using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using TriviaScapes.ClientIntegration.Contracts.Ads;

namespace TriviaScapes.Endpoint.Ads.DisplayProfiles
{
    internal class InterstitialAdDisplayConditionSettings : IInterstitialAdDisplayRuleClientIntegration, IInterstitialAdDisplayConditionClientIntegration
    {
        public const int DefaultPeriod = 2;

        private const int DefaultMinLevel = -1;
        private const int DefaultMaxLevel = -1;

        public InterstitialAdDisplayConditionSettings()
        {
            MinLevel = DefaultMinLevel;
            MaxLevel = DefaultMaxLevel;
            Period = DefaultPeriod;
        }

        [ConfigCollectionKey]
        [ConfigCollectionDisplayKey]
        [ConfigDisplayStyle(IsHeader = true)]
        public string Name => $"{MinLevel}-{MaxLevel}";

        [ConfigDisplayStyle(IsHeader = true)]
        [DefaultValue(DefaultMinLevel)]
        [Description("Any if < 0")]
        public int MinLevel { get; private set; }

        [ConfigDisplayStyle(IsHeader = true)]
        [DefaultValue(DefaultMaxLevel)]
        [Description("Any if < 0")]
        public int MaxLevel { get; private set; }

        [ConfigDisplayStyle(IsHeader = true)]
        public InterstitialAdDisplayRuleType Type { get; private set; }

        [ConfigDisplayStyle(IsHeader = true)]
        [DefaultValue(DefaultPeriod)]
        public int Period { get; private set; }

        [ConfigDisplayStyle(IsHeader = true)]
        [DefaultValue(0)]
        [Description("In seconds")]
        public int Timeout { get; private set; }

        int? IInterstitialAdDisplayConditionClientIntegration.MinLevel => MinLevel >= 0 ? MinLevel : null;

        int? IInterstitialAdDisplayConditionClientIntegration.MaxLevel => MaxLevel >= 0 ? MaxLevel : null;

        IInterstitialAdDisplayRuleClientIntegration IInterstitialAdDisplayConditionClientIntegration.Rule => this;

        TimeSpan IInterstitialAdDisplayRuleClientIntegration.Timeout => TimeSpan.FromSeconds(Timeout);

        int IInterstitialAdDisplayRuleClientIntegration.PreloadingPeriod => 0;
    }
}