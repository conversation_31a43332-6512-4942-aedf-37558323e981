using System.Collections.Generic;
using Lr.Basic.Config.Attributes;
using Newtonsoft.Json;
using TriviaScapes.ClientIntegration.Contracts.HapticFeedback;

namespace TriviaScapes.Endpoint.HapticFeedback
{
    internal class HapticFeedbackProfileSettings : IHapticFeedbackProfileClientIntegration
    {
        private Dictionary<string, string> _mappings;
        private string _description;

        [ConfigUnique]
        [ConfigDisplayStyle(IsHeader = true)]
        [ConfigCollectionDisplayKey]
        [ConfigCollectionKey]
        public string Name { get; private set; }

        [ConfigDisplayStyle(IsHeader = true, DisplayStyle = Lr.Basic.Config.Data.DisplayStyle.Json)]
        public string Mappings { get; private set; }

        public string Description => _description;

        IReadOnlyDictionary<string, string> IHapticFeedbackProfileClientIntegration.Mappings => _mappings;

        public void Synchronize()
        {
            if (string.IsNullOrEmpty(Mappings))
            {
                _mappings = null;
                _description = string.Empty;

                return;
            }

            try
            {
                _mappings = JsonConvert.DeserializeObject<Dictionary<string, string>>(Mappings);
                _description = $"{_mappings.Count} sound mappings applied";
            }
            catch
            {
                _mappings = null;
                _description = "INVALID";
            }
        }
    }
}