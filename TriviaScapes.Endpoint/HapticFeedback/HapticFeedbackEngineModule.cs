using Autofac;
using Xm.ApplicationEngine;
using Xm.ApplicationEngine.Config;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.Endpoint.HapticFeedback
{
    internal class HapticFeedbackEngineModule : EngineModule
    {
        private readonly ISerializableContractRepository _serializableContractRepository;

        public HapticFeedbackEngineModule(ISerializableContractRepository serializableContractRepository)
        {
            _serializableContractRepository = serializableContractRepository;
        }

        public override void RegisterConfigTypes(IConfigStorageBuilder configStorageBuilder)
        {
            base.RegisterConfigTypes(configStorageBuilder);

            configStorageBuilder.RegisterGameplayConfigType<HapticFeedbackConfiguration>();
            configStorageBuilder.RegisterGameplayVariantSource<HapticFeedbackProfileConfigVariantSource>();
        }

        protected override void RegisterComponents(ContainerBuilder builder)
        {
            base.RegisterComponents(builder);

            builder.RegisterType<HapticFeedbackProfileConfigRepository>()
                   .WithParameter(TypedParameter.From(_serializableContractRepository))
                   .As<IHapticFeedbackProfileRepository>()
                   .SingleInstance();
        }
    }
}