using System;
using Lr.Basic.Config;
using Xm.ApplicationEngine.Config;

namespace TriviaScapes.Endpoint.HapticFeedback
{
    [AttributeUsage(AttributeTargets.Property, AllowMultiple = false, Inherited = true)]
    internal sealed class HapticFeedbackProfileAttribute : LocalConfigVariantReferenceAttribute
    {
        protected override string LayerName => ConfigLayers.Gameplay;

        public override string GetVariantName(IConfigSerializerContext context)
        {
            return HapticFeedbackProfileConfigVariantSource.VariantName;
        }
    }
}