using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;

namespace TriviaScapes.Endpoint.HapticFeedback
{
    [DisplayName("Haptic Feedback")]
    internal class HapticFeedbackConfiguration
    {
        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public HapticFeedbackProfileSettings[] Profiles { get; private set; }

        [ConfigBound]
        private void OnConfigBound()
        {
            if (Profiles == null)
                return;

            foreach (var profile in Profiles)
            {
                profile.Synchronize();
            }
        }
    }
}