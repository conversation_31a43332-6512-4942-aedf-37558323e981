using System;
using System.Collections.Generic;
using System.Linq;
using TriviaScapes.ClientIntegration.Contracts.HapticFeedback;
using Xm.AppSettings.Abstractions;
using Xm.Caching;
using Xm.Contracts;
using Xm.Contracts.Abstractions;
using Xm.Utils;
using Xm.Utils.ChangeableObjects;

namespace TriviaScapes.Endpoint.HapticFeedback
{
    internal class HapticFeedbackProfileConfigRepository : IHapticFeedbackProfileRepository, IDisposable
    {
        private readonly IConvertedChangeableObject<IDictionary<string, IHapticFeedbackProfileClientIntegration>> _profiles;
        private readonly ISerializableContractRepository _serializableContractRepository;

        public HapticFeedbackProfileConfigRepository(
            IChangeableObject<HapticFeedbackConfiguration> configuration,
            ISerializableContractRepository serializableContractRepository
        )
        {
            _profiles = configuration.CreateConverted(c =>
                c.Profiles?.ToDictionary<HapticFeedbackProfileSettings, string, IHapticFeedbackProfileClientIntegration>(
                    p => p.Name,
                    p => p,
                    StringComparer.OrdinalIgnoreCase
                ) ?? new Dictionary<string, IHapticFeedbackProfileClientIntegration>()
            );

            _serializableContractRepository = serializableContractRepository;
        }

        public void Dispose()
        {
            _profiles?.Dispose();
        }

        public IXmAppSettings<IHapticFeedbackProfileClientIntegration> Get(string name, int appVersion)
        {
            if (!_profiles.Value.TryGetValue(name, out var profile))
            {
                throw new InvalidOperationException("Profile is not found")
                    .SetData("Name", name);
            }

            return new XmAppSettings<IHapticFeedbackProfileClientIntegration>(appVersion, _serializableContractRepository.CalculateVersion(profile), profile);
        }
    }
}