using System.Threading;
using System.Threading.Tasks;
using Autofac;
using TriviaScapes.Endpoint.Presets.Tags;
using Vx.ApiGateway.Integration;
using Xm.Adjust.Integration;
using Xm.ApiGateway.Integration;
using Xm.Commands;
using Xm.Commands.ApiGateway;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.Endpoint
{
    internal class ApiGatewayIntegrationModule : VxApiGatewayIntegrationModule
    {
        public ApiGatewayIntegrationModule(ISerializableContractRepository serializableContractRepository)
            : base(serializableContractRepository)
        {
        }

        protected override void Configure(IVxApiGatewayIntegrationRegistrar configurator)
        {
            configurator.SetHttp(
                ctx =>
                {
                    var commandHandlerFactory = ctx.Resolve<IXmCommandHandlerFactory>();

                    var presetTagProvider = ctx.Resolve<IPresetTagProvider>();

                    var attributionParser = ctx.Resolve<IXmAdjustClientAttributionParser>();

                    return (apiGatewayContext, cancellationToken) => HandleAsync(commandHandlerFactory, apiGatewayContext, presetTagProvider, attributionParser, cancellationToken);
                }
            );
        }

        private static Task HandleAsync(IXmCommandHandlerFactory commandHandlerFactory, IXmApiGatewayApiContext context, IPresetTagProvider presetTagProvider, IXmAdjustClientAttributionParser attributionParser, CancellationToken cancellationToken)
        {
            var commandContext = new CommandContext()
            {
                PresetIdentifier = context.Session.ClientContext?.Preset,
                UserIdentity = context.Session.UserIdentity,
                SessionId = context.Session.Id,
                UserAge = context.Session.ClientContext?.Age,
                DeviceIdentity = context.Session.DeviceIdentity,
                DeviceOs = context.Session.DeviceOs,
                DeviceStore = context.Session.DeviceStore,
                ClientAppStart = context.Session.ClientAppStart,
                GeoLocation = context.Session.GeoLocation,
                UserIsDebug = context.Session.UserIsDebug,
                UserIsNew = context.Session.UserIsNew,
                ClientIp = context.Session.ClientIp,
                Language = context.Session.ClientContext?.Language,
                DeviceModel = context.Session.DeviceModel,
                DeviceForm = context.Session.DeviceForm,
                DeviceSpecifications = context.Session.DeviceSpecifications,
                DeviceScreen = context.Session.DeviceScreen,
                DeviceAdvertisingIdentifier = context.Session.DeviceAdvertisingIdentifier,
                PresetTagProvider = presetTagProvider,
                AdjustClientAttributionParser = attributionParser
            };

            var commandProcessor = new XmApiGatewayCommandProcessor(context, commandContext, commandHandlerFactory);

            return commandProcessor.ProcessAsync(cancellationToken);
        }
    }
}