using System;
using System.Collections.Generic;
using TriviaScapes.ClientIntegration.Contracts;
using TriviaScapes.Endpoint.Extensions;
using TriviaScapes.Endpoint.Onboarding;
using TriviaScapes.Endpoint.Presets;
using TriviaScapes.Endpoint.Presets.Tags;
using Xm.Adjust.Integration;
using Xm.Advertisement.Commands;
using Xm.Billing.Commands;
using Xm.ClientApps.Integration.Contracts;
using Xm.Commands;
using Xm.Devices.Abstractions;
using Xm.Devices.Integration.Contracts;
using Xm.Devices.Integration.Contracts.Extensions;
using Xm.ExperimentalFeatures.Commands;
using Xm.GameActivities.Commands;
using Xm.Gameplays.Commands;
using Xm.Geo.Integration.Contracts;
using Xm.Language;
using Xm.Presets.Abstractions;
using Xm.Presets.Commands;
using Xm.Presets.Selection;
using Xm.PromoOffers.Commands;
using Xm.UserAchievements.Commands;
using Xm.UserRewards.Commands;
using Xm.Users.Integration.Contracts;
using Xm.Users.Integration.Contracts.Extensions;
using Xm.Users.Integration.Contracts.Utils;

namespace TriviaScapes.Endpoint
{
    public class CommandContext : IXmCommandContext,
        IXmPresetCommandContext<PresetCondition, PresetModificationCondition>,
        IXmAdvertisementCommandContext,
        IXmBillingCommandContext,
        IXmUserRewardCommandContext,
        IXmUserAchievementCommandContext,
        IXmExperimentalFeatureCommandContext,
        IPresetTagContext,
        IXmGameActivityCommandContext,
        IOnboardingSession,
        IXmGameplayCommandContext,
        IXmPromoOfferCommandContext
    {
        public string SessionId { get; init; }

        public int? UserAge { get; init; }

        public IXmDeviceIdentityIntegration DeviceIdentity { get; init; }

        public IXmDeviceOsIntegration DeviceOs { get; init; }

        public IXmDeviceStoreIntegration DeviceStore { get; init; }

        public IXmClientAppStartIntegration ClientAppStart { get; init; }

        public IXmGeoLocationIntegration GeoLocation { get; init; }

        public IXmPresetIdentifier PresetIdentifier { get; init; }

        public IXmUserIdentityIntegration UserIdentity { get; init; }

        public bool AppUpdated => ClientAppStart?.Transition > XmClientAppTransition.NewInstall;

        public XmLanguage? Language { get; init; }

        public string ClientIp { get; init; }

        public XmDeviceAppStoreVendor DeviceAppStoreVendor => DeviceStore?.Vendor ?? XmDeviceAppStoreVendor.Unknown;

        public bool UserIsDebug { get; init; }

        public bool UserIsNew { get; init; }

        public IXmDeviceModelIntegration DeviceModel { get; init; }

        public IXmDeviceFormIntegration DeviceForm { get; init; }

        public IXmDeviceSpecificationsIntegration DeviceSpecifications { get; init; }

        public IXmDeviceScreenIntegration DeviceScreen { get; init; }

        public IXmDeviceAdvertisingIdentifierIntegration DeviceAdvertisingIdentifier { get; init; }

        public IPresetTagProvider PresetTagProvider { get; init; }

        public IXmAdjustClientAttributionParser AdjustClientAttributionParser { get; init; }

        Guid IXmPresetSelectionContext<PresetCondition, PresetModificationCondition>.UserTrackingId => UserIdentity.TrackingId;

        bool IXmPresetSelectionContext<PresetCondition, PresetModificationCondition>.ExperimentSlotsAreSupported => ClientAppStart?.AppVersion >= AppReleaseVersions.ExperimentSlots;

        PresetModificationCondition IXmPresetSelectionContext<PresetCondition, PresetModificationCondition>.ToModificationCondition(IReadOnlyDictionary<string, string> extras)
        {
            return new PresetModificationCondition()
            {
                DeviceId = DeviceIdentity.UniqueId,
                AppUpdated = AppUpdated,
                AppVersion = ClientAppStart?.AppVersion,
                GeoCountryCode = GeoLocation?.CountryCode,
                Language = Language?.ToString(),
                OperatingSystem = DeviceOs?.Kind.ToString(),
                UserIsNew = UserIsNew,
                UserIsDebug = UserIsDebug,
                DayFromInstall = UserIdentity.GetDaysSinceCreatedOn(),
                DeviceRawModel = DeviceModel?.Raw,
                DeviceFormFactor = DeviceForm?.Factor.ToString(),
                DeviceAppStoreVendor = DeviceAppStoreVendor.ToString(),
                OperatingSystemMajorVersion = DeviceOs?.MajorVersion,
                DeviceModelName = DeviceModel?.Name,
                DeviceModelBrand = DeviceModel?.Brand,
                DeviceRAM = DeviceSpecifications?.SystemMemorySize ?? 0,
                GeoCountryName = GeoLocation?.CountryName,
                GeoCountryTier = GeoLocation?.CountryTier ?? 0,
                DeviceScreenWidth = DeviceScreen?.Width ?? 0,
                DeviceScreenHeight = DeviceScreen?.Height ?? 0,
                DeviceScreenAspectRatio = DeviceScreen?.GetAspectRatio() ?? 0,
                UserAge = UserAge,
                Tags = PresetTagProvider.Get(this),
                UserCreatedOnUnixTimestamp = UserIdentity.GetCreatedOnUnixTimestamp(),
                RemainderOfDividingPublicIdBy2 = UserIdentity.GetRemainderOfDividingPublicId(2),
                RemainderOfDividingPublicIdBy3 = UserIdentity.GetRemainderOfDividingPublicId(3),
                RemainderOfDividingPublicIdBy4 = UserIdentity.GetRemainderOfDividingPublicId(4),
                RemainderOfDividingPublicIdBy10 = UserIdentity.GetRemainderOfDividingPublicId(10)
            }.WithExtras(extras, AdjustClientAttributionParser, DeviceOs);
        }

        PresetCondition IXmPresetSelectionContext<PresetCondition, PresetModificationCondition>.ToPresetCondition(IReadOnlyDictionary<string, string> extras)
        {
            return new PresetCondition()
            {
                DeviceId = DeviceIdentity.UniqueId,
                AppVersion = ClientAppStart?.AppVersion,
                GeoCountryCode = GeoLocation?.CountryCode,
                GeoCountryTier = GeoLocation?.CountryTier,
                Ip = ClientIp,
                OperatingSystem = DeviceOs?.Kind.ToString(),
                Language = Language?.ToString(),
                UserIsDebug = UserIsDebug,
                UserAge = UserAge
            }.WithExtras(extras, AdjustClientAttributionParser, DeviceOs);
        }

        string IXmPresetSelectionContext<PresetCondition, PresetModificationCondition>.GetUserStratum()
        {
            return $"country={GeoLocation?.CountryCode};age={XmUserUtils.GetAgeRange(UserAge ?? 0)}";
        }
    }
}