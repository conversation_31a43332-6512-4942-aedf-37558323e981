using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.Endpoint.Misc.AgeRestrictions;
using TriviaScapes.Endpoint.Misc.Url;

namespace TriviaScapes.Endpoint.Misc
{
    [DisplayName("Misc")]
    public class MiscConfiguration
    {
        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public UrlSettings Urls { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public AgeRestrictionsSettings AgeRestrictions { get; private set; }
    }
}