using System.ComponentModel;
using System.Linq;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using Xm.Devices.Abstractions;

namespace TriviaScapes.Endpoint.Misc.Url
{
    public class UrlSettings
    {
        [DefaultValue("https://triviascapes.mnogogames.com/a/privacy")]
        public string PrivacyPolicy { get; set; }
        
        [DefaultValue("https://triviascapes.mnogogames.com/a/terms")]
        public string TermsAndConditions { get; set; }
        
        [DefaultValue("https://quizauthors.com/?uid={USERTRACKINGID}&lang={LANG}&proj=TriviaScapes")]
        [Description("Warning: Interpolated strings use {}")]
        public string QuizAuthors { get; set; }

        [DefaultValue("https://triviascapes.mnogogames.com/a/copyrightform?questionId={QUESTIONID}&questionTitle={QUESTIONTITLE}")]
        public string CopyrightInfringement { get; set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public StoreUrlSettings[] StoreUrls { get; set; }

        public string GetStoreUrl(XmDeviceAppStoreVendor vendor)
        {
            return StoreUrls?.FirstOrDefault(u => u.Vendor == vendor)?.StoreUrl;
        }
    }
}