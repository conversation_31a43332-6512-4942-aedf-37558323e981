using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using Xm.Devices.Abstractions;

namespace TriviaScapes.Endpoint.Misc.Url
{
    public class StoreUrlSettings
    {
        [ConfigUnique]
        [ConfigCollectionDisplayKey]
        [ConfigCollectionKey]
        [ConfigDisplayStyle(IsHeader = true)]
        public XmDeviceAppStoreVendor Vendor { get; set; }
        
        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline, IsHeader = true)]
        public string StoreUrl { get; set; }
    }
}