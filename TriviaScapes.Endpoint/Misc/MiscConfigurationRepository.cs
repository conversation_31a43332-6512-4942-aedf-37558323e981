using TriviaScapes.ClientIntegration.Contracts.Misc;
using TriviaScapes.ClientIntegration.Contracts.Misc.AgeRestrictions;
using TriviaScapes.ClientIntegration.Contracts.Misc.Urls;
using Xm.AppSettings.Abstractions;
using Xm.Contracts;
using Xm.Contracts.Abstractions;
using Xm.Devices.Abstractions;
using Xm.Utils.ChangeableObjects;
using Xm.Utils.Containers;

namespace TriviaScapes.Endpoint.Misc
{
    internal class MiscConfigurationRepository : IMiscConfigurationRepository
    {
        private readonly IValueContainer<MiscConfiguration> _configuration;
        private readonly ISerializableContractRepository _serializableContractRepository;

        public MiscConfigurationRepository(IChangeableObject<MiscConfiguration> configuration, ISerializableContractRepository serializableContractRepository)
        {
            _configuration = configuration;
            _serializableContractRepository = serializableContractRepository;
        }

        public IXmAppSettings<IMiscConfigurationClientIntegration> Get(XmDeviceAppStoreVendor storeVendor, int appVersion)
        {
            var configuration = _configuration.Value;

            var settings = new MiscConfigurationClientIntegrationDto()
            {
                Urls = new UrlSettingsClientIntegrationDto()
                {
                    PrivacyPolicy = configuration.Urls.PrivacyPolicy,
                    QuizAuthors = configuration.Urls.QuizAuthors,
                    TermsAndConditions = configuration.Urls.TermsAndConditions,
                    CopyrightInfringement = configuration.Urls.CopyrightInfringement,
                    StoreUrl = configuration.Urls.GetStoreUrl(storeVendor)
                },
                AgeRestrictions = new AgeRestrictionsClientIntegrationDto(configuration.AgeRestrictions)
            };

            return new XmAppSettings<IMiscConfigurationClientIntegration>(appVersion, _serializableContractRepository.CalculateVersion<IMiscConfigurationClientIntegration>(settings), settings);
        }
    }
}