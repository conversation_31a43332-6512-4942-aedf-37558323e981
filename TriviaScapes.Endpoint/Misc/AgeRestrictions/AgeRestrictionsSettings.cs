using System.ComponentModel;
using TriviaScapes.ClientIntegration.Contracts.Misc.AgeRestrictions;

namespace TriviaScapes.Endpoint.Misc.AgeRestrictions
{
    public class AgeRestrictionsSettings : IAgeRestrictionsClientIntegration
    {
        [DefaultValue(33)]
        public int InitialAge { get; set; }

        [DefaultValue(99)]
        public int MaxAge { get; set; }

        [DefaultValue(7)]
        public int MinAge { get; set; }
    }
}