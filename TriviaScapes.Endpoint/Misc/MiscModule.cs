using Autofac;
using Xm.ApplicationEngine;
using Xm.ApplicationEngine.Config;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.Endpoint.Misc
{
    internal class MiscModule : EngineModule
    {
        private readonly ISerializableContractRepository _serializableContractRepository;

        public MiscModule(ISerializableContractRepository serializableContractRepository)
        {
            _serializableContractRepository = serializableContractRepository;
        }
        public override void RegisterConfigTypes(IConfigStorageBuilder configStorageBuilder)
        {
            base.RegisterConfigTypes(configStorageBuilder);

            configStorageBuilder.RegisterGameplayConfigType<MiscConfiguration>();
        }

        protected override void RegisterComponents(ContainerBuilder builder)
        {
            base.RegisterComponents(builder);

            builder.RegisterType<MiscConfigurationRepository>()
                    .WithParameter(TypedParameter.From(_serializableContractRepository))
                    .As<IMiscConfigurationRepository>()
                    .SingleInstance();
        }
    }
}