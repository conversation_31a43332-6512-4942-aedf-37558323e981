using System.ComponentModel;
using System.Linq;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts.Gameplays;
using TriviaScapes.ClientIntegration.Contracts.Gameplays.Questions;
using TriviaScapes.ClientIntegration.Contracts.Gameplays.Questions.Core;
using TriviaScapes.ClientIntegration.Contracts.Inventory;
using TriviaScapes.ClientIntegration.Contracts.Presets.HardQuestion;
using TriviaScapes.Endpoint.Presets.HardQuestion;
using Xm.Gameplays;
using Xm.Gameplays.Config;

namespace TriviaScapes.Endpoint.Gameplays.Questions.Core;

[ConfigInherit]
[DisplayName("Questions.Core")]
internal class CoreQuestionGameplaySettings : XmGameplaySettings<ICoreQuestionGameplayParametersClientIntegration>
{
    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public QuestionSettings Questions { get; internal set; }

    [DefaultValue(5)]
    [Description("In seconds")]
    public int FastAnswerDuration { get; internal set; }

    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public QuestionHintKind[] AvailableHints { get; internal set; }

    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public MotivationTextSettings MotivationText { get; internal set; }

    [DefaultValue(GameplayResourceKind.Star)]
    public GlobalGameplayResourceKind Resource { get; internal set; }

    [DefaultValue(5)]
    public int QuestionReward { get; internal set; }

    [DefaultValue(5)]
    public int FastAnswerReward { get; internal set; }

    [DefaultValue(20)]
    public int LettersQuestionReward { get; internal set; }

    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public HardQuestionGameplaySettings HardQuestion { get; internal set; }

    protected override ICoreQuestionGameplayParametersClientIntegration GetParameters(XmGameplayParameterContext context)
    {
        return new CoreQuestionGameplayParametersClientIntegrationDto()
        {
            Questions = Questions.ToClientIntegrationDto(),
            FastAnswerDuration = FastAnswerDuration,
            AvailableHints = AvailableHints.ToList(),
            MotivationText = new MotivationTextSettingsClientIntegrationDto(MotivationText),
            Resource = Resource,
            QuestionReward = QuestionReward,
            FastAnswerReward = FastAnswerReward,
            LettersQuestionReward = LettersQuestionReward,
            HardQuestion = new HardQuestionGameplaySettingsClientIntegrationDto(HardQuestion)
        };
    }
}