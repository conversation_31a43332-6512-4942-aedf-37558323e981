using System.ComponentModel;
using System.Linq;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts.Gameplays;
using TriviaScapes.ClientIntegration.Contracts.Gameplays.Questions;
using TriviaScapes.ClientIntegration.Contracts.Gameplays.Questions.QuizBlitz;
using TriviaScapes.ClientIntegration.Contracts.Inventory;
using Xm.Gameplays;
using Xm.Gameplays.Config;

namespace TriviaScapes.Endpoint.Gameplays.Questions.QuizBlitz;

[ConfigInherit]
[DisplayName("Questions.QuizBlitz")]
internal class QuizBlitzGameplaySettings : XmGameplaySettings<IQuizBlitzGameplayParametersClientIntegration>
{
    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public QuestionSettings Questions { get; internal set; }

    [DefaultValue(120)]
    [Description("In seconds")]
    public int Duration { get; internal set; }

    [DefaultValue(1)]
    public int LocalLives { get; internal set; }

    [DefaultValue(5)]
    [Description("In seconds")]
    public int FastAnswerDuration { get; internal set; }

    [DefaultValue(5)]
    [Description("In seconds")]
    public int FastLettersAnswerDuration { get; internal set; }

    [DefaultValue(AnswerMode.RightAnswer)]
    public AnswerMode AnswerMode { get; internal set; }

    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public QuestionHintKind[] AvailableHints { get; internal set; }

    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public MotivationTextSettings MotivationText { get; internal set; }

    [DefaultValue(GameplayResourceKind.Coin)]
    public GlobalGameplayResourceKind Resource { get; internal set; }

    [DefaultValue(5)]
    public int FastAnswerReward { get; internal set; }

    [DefaultValue(5)]
    public int RewardPerStar { get; internal set; }

    protected override IQuizBlitzGameplayParametersClientIntegration GetParameters(XmGameplayParameterContext context)
    {
        return new QuizBlitzGameplayParametersClientIntegrationDto()
        {
            Questions = Questions.ToClientIntegrationDto(),
            Duration = Duration,
            LocalLives = LocalLives,
            FastAnswerDuration = FastAnswerDuration,
            FastLettersAnswerDuration = FastLettersAnswerDuration,
            AnswerMode = AnswerMode,
            AvailableHints = AvailableHints.ToList(),
            MotivationText = new MotivationTextSettingsClientIntegrationDto(MotivationText),
            Resource = Resource,
            FastAnswerReward = FastAnswerReward,
            RewardPerStar = RewardPerStar
        };
    }
}