using System.ComponentModel;
using System.Linq;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts.Gameplays.Questions;
using TriviaScapes.ClientIntegration.Contracts.Gameplays.Questions.Survival;
using TriviaScapes.ClientIntegration.Contracts.Inventory;
using Xm.Gameplays;
using Xm.Gameplays.Config;
using Xm.QuestionDelivery.Annotations;

namespace TriviaScapes.Endpoint.Gameplays.Questions.Survival;

[ConfigInherit]
[DisplayName("Questions.Survival")]
public class SurvivalQuizGameplaySettings : XmGameplaySettings<ISurvivalQuizGameplayParametersClientIntegration>
{
    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public QuestionHintKind[] AvailableHints { get; internal set; }

    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public SurvivalQuizBotSettings Bots { get; internal set; }

    [DefaultValue(10)]
    [Description("in seconds")]
    public float QuestionDuration { get; internal set; }

    [DefaultValue(1000)]
    [Description("coins")]
    public int Reward { get; internal set; }

    [DefaultValue(20)]
    public int QuestionCount { get; internal set; }

    [XmQuestionIssuePresetGroup]
    public string QuestionIssuePresetGroup { get; internal set; }

    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public MotivationTextSettings MotivationText { get; internal set; }

    [DefaultValue(10f)]
    public float MatchmakingDuration { get; internal set; }

    protected override ISurvivalQuizGameplayParametersClientIntegration GetParameters(XmGameplayParameterContext context)
    {
        return new SurvivalQuizGameplayParametersClientIntegrationDto()
        {
            QuestionDuration = QuestionDuration,
            Bots = new SurvivalQuizBotSettingsClientIntegrationDto(Bots),
            Reward = Reward,
            Questions = new QuestionSettingsClientIntegrationDto()
            {
                Map = QuestionLevelMapBuilder.BuildWithSingleQuestionTile(QuestionCount),
                QuestionIssuePresetGroup = QuestionIssuePresetGroup
            },
            AvailableHints = AvailableHints.ToList(),
            MatchmakingDuration = MatchmakingDuration,
            MotivationText = new MotivationTextSettingsClientIntegrationDto(MotivationText)
        };
    }
}