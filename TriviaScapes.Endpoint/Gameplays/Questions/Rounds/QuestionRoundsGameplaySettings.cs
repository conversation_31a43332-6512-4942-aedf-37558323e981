using System.ComponentModel;
using System.Linq;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts.Gameplays;
using TriviaScapes.ClientIntegration.Contracts.Gameplays.Questions;
using TriviaScapes.ClientIntegration.Contracts.Gameplays.Questions.Rounds;
using TriviaScapes.ClientIntegration.Contracts.Inventory;
using Xm.Gameplays;
using Xm.Gameplays.Config;

namespace TriviaScapes.Endpoint.Gameplays.Questions.Rounds;

[ConfigInherit]
[DisplayName("Questions.Rounds")]
internal class QuestionRoundsGameplaySettings : XmGameplaySettings<IQuestionRoundsGameplayParametersClientIntegration>
{
    private const GlobalGameplayResourceKind DefaultResource = GlobalGameplayResourceKind.Crystal;
    private const int DefaultCompletionReward = 300;

    public QuestionRoundsGameplaySettings()
    {
        Resource = DefaultResource;
        CompletionReward = DefaultCompletionReward;
    }

    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public QuestionRoundSettings[] Rounds { get; internal set; }

    [DefaultValue(3)]
    [Description("max question category rerolls")]
    public int MaxCategorySelectionAttempts { get; internal set; }

    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public QuestionHintKind[] AvailableHints { get; internal set; }

    [DefaultValue(15)]
    [Description("In seconds")]
    public int QuestionAnswerTimerDuration { get; internal set; }

    [DefaultValue(100)]
    [Description("0-100%")]
    public float MotivationTextRightAnswerShowPercentage { get; internal set; }

    [DefaultValue(DefaultResource)]
    public GlobalGameplayResourceKind Resource { get; internal set; }

    [DefaultValue(DefaultCompletionReward)]
    public int CompletionReward { get; internal set; }

    protected override IQuestionRoundsGameplayParametersClientIntegration GetParameters(XmGameplayParameterContext context)
    {
        return new QuestionRoundsGameplayParametersClientIntegrationDto()
        {
            Rounds = Rounds.Select(r => new QuestionRoundClientIntegrationDto(r)).ToList(),
            AvailableHints = AvailableHints.ToList(),
            QuestionAnswerTimerDuration = QuestionAnswerTimerDuration,
            MaxCategorySelectionAttempts = MaxCategorySelectionAttempts,
            MotivationText = new MotivationTextSettingsClientIntegrationDto()
            {
                WrongAnswerShowPercentage = 0f,
                RightAnswerShowPercentage = MotivationTextRightAnswerShowPercentage
            },
            Resource = Resource,
            CompletionReward = CompletionReward
        };
    }
}