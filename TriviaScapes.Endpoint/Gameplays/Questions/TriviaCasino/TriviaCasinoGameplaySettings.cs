using System;
using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts.Gameplays.Questions.TriviaCasino;
using Xm.Gameplays;
using Xm.Gameplays.Config;

namespace TriviaScapes.Endpoint.Gameplays.Questions.TriviaCasino;

[ConfigInherit]
[DisplayName("Questions.Trivia Casino")]
public class TriviaCasinoGameplaySettings : XmGameplaySettings<ITriviaCasinoGameplayParametersClientIntegration>
{
    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public QuestionSettings Questions { get; internal set; }

    [DefaultValue(0)]
    [Description("In seconds. If val <= 0 - no timer")]
    public int Duration { get; internal set; }

    [DefaultValue(5)]
    [Description("In seconds")]
    public int WrongAnswerPenalty { get; internal set; }

    [DefaultValue(25)]
    public int CoinsPerChip { get; internal set; }

    protected override ITriviaCasinoGameplayParametersClientIntegration GetParameters(XmGameplayParameterContext context)
    {
        return new TriviaCasinoGameplayParametersClientIntegrationDto()
        {
            Questions = Questions.ToClientIntegrationDto(),
            Duration = TimeSpan.FromSeconds(Duration),
            WrongAnswerPenalty = TimeSpan.FromSeconds(WrongAnswerPenalty),
            CoinsPerChip = CoinsPerChip,
        };
    }
}